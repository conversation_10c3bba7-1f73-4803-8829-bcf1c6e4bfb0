# AI SiteForge Frontend

A modern web application that generates websites from text prompts and screenshots using AI technology.

## 🚀 Features

### MVP Scope (Completed)
- ✅ **User Input** - Prompt entry and screenshot upload interface
- ✅ **AI Generation** - Frontend for AI-powered website generation 
- ✅ **Tech Stack Choice** - HTML/CSS/JS vs React + Tailwind CSS selection
- ✅ **Preview Environment** - Secure iframe/sandbox for website preview
- ✅ **Natural Language Edits** - Text-based modification commands
- ✅ **Client Review Portal** - Secure sharing with commenting system
- ✅ **Export & Payment** - Stripe integration UI for downloads/deployment

### Additional Features
- ✅ **Landing Page** - Modern marketing site with feature highlights
- ✅ **Dashboard** - Project management and overview
- ✅ **Examples Gallery** - Showcase of generated websites
- ✅ **Success Pages** - Post-purchase confirmation flow

## 🛠 Tech Stack

- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Icons**: Lucide React
- **Components**: Custom UI component library
- **State Management**: React hooks (useState, useEffect)

## 📁 Project Structure

```
src/
├── app/                      # Next.js App Router pages
│   ├── create/              # Website creation flow
│   │   ├── page.tsx        # Prompt input screen
│   │   └── tech-stack/     # Technology selection
│   ├── project/            # Project management
│   │   ├── preview/        # Website preview with editing
│   │   └── export/         # Export and payment flow
│   ├── share/[id]/         # Client review portal
│   ├── dashboard/          # User dashboard
│   ├── examples/           # Example websites
│   └── success/            # Success confirmation
├── components/             # Reusable components
│   ├── ui/                # Basic UI components
│   ├── layout/            # Header, footer components
│   └── features/          # Feature-specific components
├── lib/                   # Utility functions
├── types/                 # TypeScript type definitions
└── utils/                 # Helper utilities
```

## 🚦 Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone [repository-url]
   cd frontend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Open in browser**
   ```
   http://localhost:3000
   ```

## 🎯 User Flow

1. **Landing Page** (`/`) - Marketing page with CTA
2. **Input Screen** (`/create`) - Enter prompt + upload screenshot
3. **Tech Stack Selection** (`/create/tech-stack`) - Choose framework
4. **Preview** (`/project/preview`) - View generated site + make edits
5. **Client Review** (`/share/[id]`) - Share with clients for feedback
6. **Export** (`/project/export`) - Purchase and download/deploy
7. **Success** (`/success`) - Confirmation and next steps

## 🧩 Key Components

### WebsitePreview
- Secure iframe rendering of generated code
- Multiple viewport sizes (desktop, tablet, mobile)
- Refresh functionality

### EditPanel  
- Natural language command input
- Example suggestions
- Edit history tracking

### CommentSystem
- Client feedback interface
- Real-time comment management
- Position-based commenting

### Tech Stack Selection
- Visual comparison of options
- Feature highlighting
- Clear pricing

## 🎨 Design System

### Colors
- **Primary**: Blue (blue-600)
- **Secondary**: Purple (purple-600)
- **Success**: Green (green-600)
- **Warning**: Orange (orange-600)
- **Error**: Red (red-600)

### Components
- **Cards**: Rounded, shadow-based design
- **Buttons**: Multiple variants (default, outline, ghost)
- **Forms**: Clean inputs with proper validation states
- **Layout**: Container-based with responsive grid

## 📱 Responsive Design

- **Mobile-first** approach with Tailwind CSS
- **Breakpoints**: 
  - `sm`: 640px
  - `md`: 768px  
  - `lg`: 1024px
  - `xl`: 1280px

## 🔧 Backend Integration Points

The frontend is designed to integrate with backend APIs:

### Required Endpoints
```typescript
// Generation
POST /api/generate
POST /api/edit

// Projects  
GET /api/projects
GET /api/projects/:id
PUT /api/projects/:id

// Comments
GET /api/projects/:id/comments
POST /api/projects/:id/comments

// Payments
POST /api/payments/checkout
POST /api/export/:id

// Auth
POST /api/auth/login
POST /api/auth/logout
GET /api/auth/me
```

### Data Flow
1. User inputs → Frontend validation → API calls
2. AI generation → Real-time status updates
3. Preview updates → Live iframe refresh
4. Comments → Real-time synchronization
5. Payments → Stripe webhook handling

## 🚀 Deployment

### Build for Production
```bash
npm run build
npm start
```

### Environment Variables
```bash
NEXT_PUBLIC_API_URL=https://api.siteforge.com
NEXT_PUBLIC_STRIPE_PUBLIC_KEY=pk_...
```

### Deployment Platforms
- **Vercel** (recommended for Next.js)
- **Netlify**
- **AWS Amplify**
- **Traditional hosting** with Node.js

## 📋 TODO for Backend Integration

1. **API Client Setup**
   - Add axios/fetch wrapper
   - Error handling middleware
   - Request/response interceptors

2. **Authentication**
   - JWT token management
   - Protected routes
   - User session handling

3. **Real-time Features**
   - WebSocket connections for live updates
   - Server-sent events for generation status

4. **File Upload**
   - Image upload for screenshots
   - File validation and processing

5. **Payment Integration**
   - Complete Stripe Elements integration
   - Webhook handling
   - Order management

## 🔒 Security Considerations

- **Input Validation**: All user inputs sanitized
- **XSS Prevention**: Proper content sanitization
- **CSRF Protection**: Token-based protection
- **File Upload Security**: Type and size validation
- **Iframe Sandboxing**: Secure preview rendering

## 🧪 Testing Strategy

### Recommended Testing Setup
```bash
# Install testing dependencies
npm install --save-dev jest @testing-library/react @testing-library/jest-dom

# Component testing
npm run test

# E2E testing
npm install --save-dev @playwright/test
npm run test:e2e
```

## 📈 Performance Optimization

- **Code Splitting**: Automatic with Next.js
- **Image Optimization**: Next.js Image component
- **Bundle Analysis**: `npm run analyze`
- **Lazy Loading**: Components and routes
- **Caching**: Static generation where possible

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For questions and support:
- Create an issue in this repository
- Email: <EMAIL>
- Documentation: [docs.siteforge.com](https://docs.siteforge.com)

---

Built with ❤️ using Next.js and TypeScript