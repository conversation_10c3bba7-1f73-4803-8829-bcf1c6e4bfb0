export interface Project {
  id: string;
  name: string;
  prompt: string;
  screenshot?: string;
  techStack: 'html' | 'react';
  generatedCode: string;
  previewUrl?: string;
  shareLink?: string;
  status: 'draft' | 'generating' | 'ready' | 'exported';
  createdAt: Date;
  updatedAt: Date;
}

export interface Comment {
  id: string;
  projectId: string;
  author: string;
  content: string;
  position?: {
    x: number;
    y: number;
  };
  createdAt: Date;
}

export interface User {
  id: string;
  email: string;
  name?: string;
  role: 'owner' | 'client';
}

export interface TechStack {
  id: 'html' | 'react';
  name: string;
  description: string;
  icon: string;
}

export interface EditCommand {
  id: string;
  projectId: string;
  command: string;
  appliedAt: Date;
  status: 'pending' | 'applied' | 'failed';
}

export interface GenerationRequest {
  prompt: string;
  screenshot?: File;
  techStack: 'html' | 'react';
}

export interface ExportOptions {
  format: 'zip' | 'netlify' | 'vercel';
  price: number;
}
