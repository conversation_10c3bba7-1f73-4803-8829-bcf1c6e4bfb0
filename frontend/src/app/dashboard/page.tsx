"use client"

import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  Plus, 
  Eye, 
  Share2, 
  Download, 
  MoreHorizontal,
  Calendar,
  Code,
  MessageSquare,
  Clock,
  CheckCircle,
  AlertCircle
} from "lucide-react"

// Mock project data
const projects = [
  {
    id: "project-1",
    name: "Portfolio Website",
    prompt: "Modern portfolio for a graphic designer with dark theme and animations",
    techStack: "react" as const,
    status: "ready" as const,
    createdAt: new Date("2024-01-15"),
    updatedAt: new Date("2024-01-15"),
    comments: 3,
    shares: 2
  },
  {
    id: "project-2", 
    name: "Restaurant Landing",
    prompt: "Warm restaurant website with menu and reservation system",
    techStack: "html" as const,
    status: "generating" as const,
    createdAt: new Date("2024-01-14"),
    updatedAt: new Date("2024-01-14"),
    comments: 0,
    shares: 0
  },
  {
    id: "project-3",
    name: "SaaS Product Page",
    prompt: "Professional SaaS landing with pricing and features",
    techStack: "react" as const,
    status: "exported" as const,
    createdAt: new Date("2024-01-10"),
    updatedAt: new Date("2024-01-12"),
    comments: 8,
    shares: 5
  },
  {
    id: "project-4",
    name: "E-commerce Store",
    prompt: "Online clothing store with product catalog and cart",
    techStack: "react" as const,
    status: "draft" as const,
    createdAt: new Date("2024-01-08"),
    updatedAt: new Date("2024-01-08"),
    comments: 1,
    shares: 0
  }
]

const getStatusColor = (status: string) => {
  switch (status) {
    case "ready": return "text-slate-600 bg-slate-100"
    case "generating": return "text-slate-600 bg-slate-100"
    case "exported": return "text-slate-600 bg-slate-100"
    case "draft": return "text-slate-500 bg-slate-50"
    default: return "text-slate-500 bg-slate-50"
  }
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case "ready": return CheckCircle
    case "generating": return Clock
    case "exported": return Download
    case "draft": return AlertCircle
    default: return AlertCircle
  }
}

export default function DashboardPage() {
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    }).format(date)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-stone-50 relative">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-slate-200/30 to-stone-200/30 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-stone-200/30 to-slate-200/30 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 py-20 relative">
        {/* Header */}
        <div className="flex items-center justify-between mb-16">
          <div>
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-white border border-slate-200 mb-4 shadow-sm">
              <Code className="h-4 w-4 text-slate-600 mr-2" />
              <span className="text-sm font-medium text-slate-700">Project Management</span>
            </div>
            <h1 className="text-5xl md:text-7xl font-bold text-slate-900 mb-6 leading-tight">
              Your <span className="text-slate-700">Dashboard</span>
            </h1>
            <p className="text-xl md:text-2xl text-slate-600 leading-relaxed max-w-3xl">
              Manage your AI-generated websites
            </p>
          </div>
          <Link href="/create">
            <Button size="lg" className="text-lg px-8 py-6">
              <Plus className="mr-2 h-5 w-5" />
              New Website
            </Button>
          </Link>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
          <Card className="border-slate-200 shadow-sm hover:shadow-md transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">Total Projects</p>
                  <p className="text-3xl font-bold text-slate-900">4</p>
                </div>
                <div className="w-12 h-12 bg-slate-100 rounded-xl flex items-center justify-center">
                  <Code className="h-6 w-6 text-slate-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-slate-200 shadow-sm hover:shadow-md transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">Ready to Export</p>
                  <p className="text-3xl font-bold text-slate-900">1</p>
                </div>
                <div className="w-12 h-12 bg-slate-100 rounded-xl flex items-center justify-center">
                  <CheckCircle className="h-6 w-6 text-slate-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-slate-200 shadow-sm hover:shadow-md transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">Total Comments</p>
                  <p className="text-3xl font-bold text-slate-900">12</p>
                </div>
                <div className="w-12 h-12 bg-slate-100 rounded-xl flex items-center justify-center">
                  <MessageSquare className="h-6 w-6 text-slate-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-slate-200 shadow-sm hover:shadow-md transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">Times Shared</p>
                  <p className="text-3xl font-bold text-slate-900">7</p>
                </div>
                <div className="w-12 h-12 bg-slate-100 rounded-xl flex items-center justify-center">
                  <Share2 className="h-6 w-6 text-slate-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Projects List */}
        <Card className="border-slate-200 shadow-sm">
          <CardHeader>
            <CardTitle>Your Projects</CardTitle>
            <CardDescription>
              All your AI-generated websites in one place
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {projects.map((project) => {
                const StatusIcon = getStatusIcon(project.status)
                
                return (
                  <div
                    key={project.id}
                    className="flex items-center justify-between p-4 border border-slate-200 rounded-lg hover:bg-slate-50 transition-colors"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-slate-100 rounded-lg flex items-center justify-center">
                        <Code className="h-6 w-6 text-slate-600" />
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <h3 className="font-semibold text-slate-900">{project.name}</h3>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(project.status)}`}>
                            <StatusIcon className="w-3 h-3 mr-1" />
                            {project.status}
                          </span>
                          <span className="text-xs text-slate-500 bg-slate-100 px-2 py-1 rounded">
                            {project.techStack === 'html' ? 'HTML/CSS/JS' : 'React + Tailwind'}
                          </span>
                        </div>
                        <p className="text-sm text-slate-600 mt-1 line-clamp-1">
                          {project.prompt}
                        </p>
                        <div className="flex items-center space-x-4 mt-2 text-xs text-slate-500">
                          <div className="flex items-center">
                            <Calendar className="w-3 h-3 mr-1" />
                            Created {formatDate(project.createdAt)}
                          </div>
                          {project.comments > 0 && (
                            <div className="flex items-center">
                              <MessageSquare className="w-3 h-3 mr-1" />
                              {project.comments} comments
                            </div>
                          )}
                          {project.shares > 0 && (
                            <div className="flex items-center">
                              <Share2 className="w-3 h-3 mr-1" />
                              {project.shares} shares
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      {project.status === 'ready' && (
                        <>
                          <Link href={`/project/preview?id=${project.id}`}>
                            <Button variant="outline" size="sm">
                              <Eye className="mr-1 h-4 w-4" />
                              Preview
                            </Button>
                          </Link>
                          <Link href={`/share/${project.id}`}>
                            <Button variant="outline" size="sm">
                              <Share2 className="mr-1 h-4 w-4" />
                              Share
                            </Button>
                          </Link>
                          <Link href={`/project/export?id=${project.id}`}>
                            <Button size="sm">
                              <Download className="mr-1 h-4 w-4" />
                              Export
                            </Button>
                          </Link>
                        </>
                      )}
                      
                      {project.status === 'generating' && (
                        <Button variant="outline" size="sm" disabled>
                          <Clock className="mr-1 h-4 w-4 animate-pulse" />
                          Generating...
                        </Button>
                      )}
                      
                      {project.status === 'exported' && (
                        <>
                          <Link href={`/project/preview?id=${project.id}`}>
                            <Button variant="outline" size="sm">
                              <Eye className="mr-1 h-4 w-4" />
                              View
                            </Button>
                          </Link>
                          <Button variant="outline" size="sm">
                            <Download className="mr-1 h-4 w-4" />
                            Re-download
                          </Button>
                        </>
                      )}
                      
                      {project.status === 'draft' && (
                        <Link href={`/create/tech-stack?prompt=${encodeURIComponent(project.prompt)}`}>
                          <Button size="sm">
                            Continue
                          </Button>
                        </Link>
                      )}
                      
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
