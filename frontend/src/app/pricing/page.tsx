"use client"

import <PERSON> from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  Check, 
  ArrowRight, 
  Sparkles, 
  Zap, 
  Star, 
  Crown,
  FileCode,
  Globe,
  Users,
  Shield,
  Clock,
  Download,
  MessageSquare,
  Palette,
  Code
} from "lucide-react"

// Pricing plans
const plans = [
  {
    name: "Starter",
    description: "Perfect for trying out AI SiteForge",
    price: "Free",
    period: "",
    popular: false,
    features: [
      "1 website generation per month",
      "HTML/CSS/JS stack only",
      "Basic preview",
      "Download ZIP export",
      "Community support",
      "Standard templates"
    ],
    limitations: [
      "No React + Tailwind",
      "No client sharing",
      "No custom domains",
      "Limited editing"
    ],
    buttonText: "Get Started Free",
    buttonVariant: "outline" as const,
    icon: FileCode,
    color: "gray"
  },
  {
    name: "Pro",
    description: "For professionals and small teams",
    price: "$29",
    period: "/month",
    popular: true,
    features: [
      "Unlimited website generations",
      "All tech stacks (HTML/CSS/JS + React)",
      "Advanced preview with all viewports",
      "All export options (ZIP, Netlify, Vercel)",
      "Client review portal with comments",
      "Natural language editing",
      "Priority support",
      "Custom branding",
      "Advanced templates",
      "Screenshot references"
    ],
    limitations: [],
    buttonText: "Start Pro Trial",
    buttonVariant: "default" as const,
    icon: Zap,
    color: "blue"
  },
  {
    name: "Enterprise",
    description: "For agencies and large teams",
    price: "$99",
    period: "/month",
    popular: false,
    features: [
      "Everything in Pro",
      "Team collaboration tools",
      "White-label solutions",
      "API access",
      "Custom integrations",
      "Dedicated account manager",
      "SLA guarantee",
      "Advanced analytics",
      "Custom AI training",
      "Bulk operations",
      "SSO integration",
      "Advanced security"
    ],
    limitations: [],
    buttonText: "Contact Sales",
    buttonVariant: "outline" as const,
    icon: Crown,
    color: "purple"
  }
]

// Additional features
const additionalFeatures = [
  {
    icon: Clock,
    title: "Lightning Fast",
    description: "Generate websites in under 20 seconds with our optimized AI pipeline"
  },
  {
    icon: Shield,
    title: "Secure & Private",
    description: "Your data is encrypted and never shared. SOC 2 compliant infrastructure"
  },
  {
    icon: Globe,
    title: "Global CDN",
    description: "Deploy to worldwide edge locations for optimal performance"
  },
  {
    icon: Users,
    title: "Team Collaboration",
    description: "Work together with real-time collaboration and project sharing"
  }
]

// FAQ data
const faqs = [
  {
    question: "Can I change plans anytime?",
    answer: "Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately and billing is prorated."
  },
  {
    question: "What's included in the free plan?",
    answer: "The free plan includes 1 website generation per month, HTML/CSS/JS stack, basic preview, and ZIP download export."
  },
  {
    question: "Do you offer refunds?",
    answer: "Yes, we offer a 30-day money-back guarantee for all paid plans. No questions asked."
  },
  {
    question: "Can I use my own domain?",
    answer: "Yes, Pro and Enterprise plans support custom domains for your generated websites."
  },
  {
    question: "Is there an API available?",
    answer: "API access is available for Enterprise customers. Contact our sales team to learn more."
  },
  {
    question: "How does the AI generate websites?",
    answer: "Our AI uses advanced language models combined with computer vision to understand your requirements and generate pixel-perfect websites."
  }
]

export default function PricingPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-stone-50 relative">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-slate-200/30 to-stone-200/30 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-stone-200/30 to-slate-200/30 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 py-20 relative">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-white border border-slate-200 mb-8 shadow-sm">
            <Sparkles className="h-4 w-4 text-slate-600 mr-2" />
            <span className="text-sm font-medium text-slate-700">Simple, Transparent Pricing</span>
          </div>
          
          <h1 className="text-5xl md:text-7xl font-bold text-slate-900 mb-6 leading-tight">
            Choose the Perfect 
            <span className="block text-slate-700">
              Plan for You
            </span>
          </h1>
          
          <p className="text-xl md:text-2xl text-slate-600 max-w-3xl mx-auto leading-relaxed mb-8">
            Start free and scale as you grow. All plans include our core AI website generation technology.
          </p>

          {/* Plan Toggle */}
          <div className="inline-flex items-center p-1 bg-white rounded-full border border-slate-200 shadow-sm">
            <button className="px-6 py-2 rounded-full bg-slate-900 text-white text-sm font-medium transition-all duration-300">
              Monthly
            </button>
            <button className="px-6 py-2 rounded-full text-slate-600 text-sm font-medium hover:text-slate-900 transition-all duration-300">
              Annual <span className="text-green-600 text-xs ml-1">Save 20%</span>
            </button>
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-20">
          {plans.map((plan, index) => {
            const Icon = plan.icon
            
            return (
              <Card
                key={plan.name}
                className={`relative border-2 transition-all duration-300 ${
                  plan.popular
                    ? 'border-slate-500 bg-slate-50 shadow-lg'
                    : 'border-slate-200 hover:border-slate-300 hover:shadow-md'
                }`}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <div className="bg-slate-900 text-white px-4 py-1 rounded-full text-sm font-medium">
                      Most Popular
                    </div>
                  </div>
                )}
                
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-12 h-12 bg-slate-100 rounded-xl flex items-center justify-center">
                      <Icon className="h-6 w-6 text-slate-600" />
                    </div>
                    {plan.popular && (
                      <Star className="h-6 w-6 text-yellow-500 fill-current" />
                    )}
                  </div>
                  
                  <CardTitle className="text-2xl">{plan.name}</CardTitle>
                  <CardDescription className="text-gray-600">
                    {plan.description}
                  </CardDescription>
                  
                  <div className="mt-6">
                    <div className="flex items-baseline">
                      <span className="text-4xl font-bold text-slate-900">{plan.price}</span>
                      <span className="text-slate-600 ml-1">{plan.period}</span>
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-6">
                  <Button
                    className="w-full text-lg py-6"
                    variant={plan.buttonVariant}
                  >
                    {plan.buttonText}
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                  
                  <div>
                    <h4 className="font-medium text-slate-900 mb-3">What's included:</h4>
                    <ul className="space-y-2">
                      {plan.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-start">
                          <Check className="h-4 w-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                          <span className="text-sm text-slate-600">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  {plan.limitations.length > 0 && (
                    <div>
                      <h4 className="font-medium text-gray-900 mb-3">Limitations:</h4>
                      <ul className="space-y-2">
                        {plan.limitations.map((limitation, limitationIndex) => (
                          <li key={limitationIndex} className="flex items-start">
                            <span className="w-4 h-4 text-gray-400 mt-0.5 mr-2 flex-shrink-0">×</span>
                            <span className="text-sm text-gray-500">{limitation}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Additional Features */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
              Why Choose AI SiteForge?
            </h2>
            <p className="text-xl text-slate-600 max-w-2xl mx-auto">
              Built for modern teams who need fast, reliable website generation
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {additionalFeatures.map((feature, index) => (
              <Card key={index} className="border-slate-200 shadow-sm hover:shadow-md transition-all duration-300 text-center">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-slate-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                    <feature.icon className="h-6 w-6 text-slate-600" />
                  </div>
                  <h3 className="font-semibold text-slate-900 mb-2">{feature.title}</h3>
                  <p className="text-sm text-slate-600">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* FAQ Section */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
              Frequently Asked Questions
            </h2>
            <p className="text-xl text-slate-600 max-w-2xl mx-auto">
              Everything you need to know about AI SiteForge pricing and features
            </p>
          </div>
          
          <div className="max-w-3xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {faqs.map((faq, index) => (
                <Card key={index} className="border-slate-200 shadow-sm hover:shadow-md transition-all duration-300">
                  <CardContent className="p-6">
                    <h3 className="font-semibold text-slate-900 mb-3">{faq.question}</h3>
                    <p className="text-slate-600 text-sm leading-relaxed">{faq.answer}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="relative overflow-hidden">
          <div className="absolute inset-0 bg-slate-900 rounded-3xl"></div>
          <div className="absolute inset-0 bg-gradient-to-br from-black/20 to-transparent rounded-3xl"></div>
          <div className="relative px-8 py-20 text-center text-white">
            <div className="absolute top-6 left-6 w-12 h-12 bg-white/10 rounded-2xl backdrop-blur-sm"></div>
            <div className="absolute top-6 right-6 w-8 h-8 bg-white/10 rounded-xl backdrop-blur-sm"></div>
            <div className="absolute bottom-6 left-1/4 w-6 h-6 bg-white/10 rounded-lg backdrop-blur-sm"></div>
            <div className="absolute bottom-6 right-1/3 w-4 h-4 bg-white/10 rounded-md backdrop-blur-sm"></div>
            
            <div className="max-w-3xl mx-auto">
              <div className="inline-flex items-center px-4 py-2 rounded-full bg-white/20 backdrop-blur-sm border border-white/30 mb-8">
                <Star className="h-4 w-4 mr-2" />
                <span className="text-sm font-medium">Start Building Today</span>
              </div>
              
              <h2 className="text-4xl md:text-5xl font-bold mb-6 leading-tight">
                Ready to Transform Your 
                <span className="block text-slate-300">
                  Web Development?
                </span>
              </h2>
              
              <p className="text-xl md:text-2xl mb-12 opacity-90 leading-relaxed">
                Join thousands of developers and agencies who trust AI SiteForge to create stunning websites in seconds
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/create">
                  <Button size="lg" className="text-lg px-10 py-6 bg-white text-slate-900 hover:bg-slate-100">
                    Start Free Trial <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
                <Link href="/examples">
                  <Button size="lg" variant="outline" className="text-lg px-10 py-6 border-white/30 text-white hover:bg-white/10">
                    View Examples
                  </Button>
                </Link>
              </div>
              
              <div className="mt-12 flex flex-wrap justify-center items-center gap-6 text-sm opacity-75">
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                  Free 7-day trial
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-blue-400 rounded-full mr-2"></div>
                  No credit card required
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-purple-400 rounded-full mr-2"></div>
                  Cancel anytime
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
