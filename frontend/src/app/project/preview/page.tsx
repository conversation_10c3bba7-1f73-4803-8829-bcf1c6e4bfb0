"use client"

import { useState, useEffect, Suspense } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { EnhancedWebsitePreview } from "@/components/features/enhanced-website-preview"
import { ComprehensiveEditPanel } from "@/components/features/comprehensive-edit-panel"
import { LayersPanel } from "@/components/features/layers-panel"
import { PageManager } from "@/components/features/page-manager"
import { DesignSystemPanel } from "@/components/features/design-system-panel"
import { PropertiesPanel } from "@/components/features/properties-panel"
import { EditorToolbar } from "@/components/features/editor-toolbar"
import {
  Share2,
  Download,
  ArrowLeft,
  CheckCircle,
  Code,
  Eye,
  ExternalLink,
  Sparkles,
  Undo,
  Redo,
  Layers,
  Settings,
  Grid,
  FileText,
  Palette
} from "lucide-react"

// <PERSON><PERSON> generated code for demonstration
const mockGeneratedCode = {
  html: `
    <div style="font-family: Arial, sans-serif; margin: 0; padding: 0;">
      <header style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 4rem 2rem; text-align: center;">
        <h1 style="font-size: 3rem; margin: 0 0 1rem 0; font-weight: bold;">Welcome to Our Website</h1>
        <p style="font-size: 1.2rem; opacity: 0.9; margin: 0;">Beautiful, modern design created by AI</p>
        <button style="background: white; color: #667eea; border: none; padding: 1rem 2rem; margin-top: 2rem; border-radius: 50px; font-weight: bold; cursor: pointer; font-size: 1rem;">Get Started</button>
      </header>
      
      <section style="padding: 4rem 2rem; max-width: 1200px; margin: 0 auto;">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
          <div style="padding: 2rem; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
            <h3 style="color: #333; margin-bottom: 1rem;">Feature One</h3>
            <p style="color: #666; line-height: 1.6;">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt.</p>
          </div>
          <div style="padding: 2rem; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
            <h3 style="color: #333; margin-bottom: 1rem;">Feature Two</h3>
            <p style="color: #666; line-height: 1.6;">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt.</p>
          </div>
          <div style="padding: 2rem; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
            <h3 style="color: #333; margin-bottom: 1rem;">Feature Three</h3>
            <p style="color: #666; line-height: 1.6;">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt.</p>
          </div>
        </div>
      </section>
      
      <footer style="background: #333; color: white; text-align: center; padding: 2rem;">
        <p>&copy; 2024 Your Website. All rights reserved.</p>
      </footer>
    </div>
  `,
  react: `
    const App = () => {
      return (
        <div className="min-h-screen bg-white">
          <header className="bg-gradient-to-r from-blue-500 to-purple-600 text-white py-20 px-8 text-center">
            <h1 className="text-5xl font-bold mb-4">Welcome to Our Website</h1>
            <p className="text-xl opacity-90 mb-8">Beautiful, modern design created by AI</p>
            <button className="bg-white text-blue-600 px-8 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors">
              Get Started
            </button>
          </header>
          
          <section className="py-16 px-8 max-w-6xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="p-8 bg-white rounded-lg shadow-lg text-center">
                <h3 className="text-xl font-bold text-gray-800 mb-4">Feature One</h3>
                <p className="text-gray-600 leading-relaxed">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt.</p>
              </div>
              <div className="p-8 bg-white rounded-lg shadow-lg text-center">
                <h3 className="text-xl font-bold text-gray-800 mb-4">Feature Two</h3>
                <p className="text-gray-600 leading-relaxed">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt.</p>
              </div>
              <div className="p-8 bg-white rounded-lg shadow-lg text-center">
                <h3 className="text-xl font-bold text-gray-800 mb-4">Feature Three</h3>
                <p className="text-gray-600 leading-relaxed">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt.</p>
              </div>
            </div>
          </section>
          
          <footer className="bg-gray-800 text-white text-center py-8">
            <p>&copy; 2024 Your Website. All rights reserved.</p>
          </footer>
        </div>
      );
    };
    
    ReactDOM.render(<App />, document.getElementById('root'));
  `
}

interface WebsiteElement {
  id: string
  type: 'text' | 'image' | 'button' | 'section' | 'container' | 'header' | 'footer'
  content: string
  styles: Record<string, string>
  position: { x: number; y: number; width: number; height: number }
  parent?: string
  children?: string[]
  locked?: boolean
  visible?: boolean
  animations?: {
    type: 'fade' | 'slide' | 'scale' | 'none'
    duration: number
    delay: number
  }
  borders?: {
    width: string
    style: string
    color: string
    radius: string
  }
  shadows?: {
    x: string
    y: string
    blur: string
    spread: string
    color: string
  }
}

interface WebsitePage {
  id: string
  name: string
  title: string
  slug: string
  metaDescription: string
  elements: WebsiteElement[]
  template?: 'home' | 'about' | 'contact' | 'services' | 'portfolio' | 'blog' | 'custom'
}

interface WebsiteState {
  pages: WebsitePage[]
  currentPageId: string
  globalStyles: {
    backgroundColor: string
    primaryColor: string
    secondaryColor: string
    textColor: string
    fontFamily: string
    fontSize: string
  }
  layout: {
    spacing: string
    padding: string
    margin: string
  }
  designSystem: {
    components: any[]
    colors: string[]
    typography: any[]
  }
  grid: {
    enabled: boolean
    size: number
    snap: boolean
  }
}

function PreviewPageContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [prompt, setPrompt] = useState("")
  const [techStack, setTechStack] = useState<'html' | 'react'>('html')
  const [generatedCode, setGeneratedCode] = useState("")
  const [isEditProcessing, setIsEditProcessing] = useState(false)
  const [projectId] = useState(`project-${Date.now()}`)

  // Enhanced state management for editing
  const [websiteState, setWebsiteState] = useState<WebsiteState>({
    pages: [],
    currentPageId: '',
    globalStyles: {
      backgroundColor: '#ffffff',
      primaryColor: '#667eea',
      secondaryColor: '#764ba2',
      textColor: '#333333',
      fontFamily: 'Arial, sans-serif',
      fontSize: '16px'
    },
    layout: {
      spacing: '1rem',
      padding: '2rem',
      margin: '0'
    },
    designSystem: {
      components: [],
      colors: ['#667eea', '#764ba2', '#ffffff', '#333333'],
      typography: []
    },
    grid: {
      enabled: false,
      size: 20,
      snap: true
    }
  })

  // Undo/Redo functionality
  const [history, setHistory] = useState<WebsiteState[]>([])
  const [historyIndex, setHistoryIndex] = useState(-1)
  const [selectedElement, setSelectedElement] = useState<string | null>(null)
  const [selectedElements, setSelectedElements] = useState<string[]>([])
  const [activePanels, setActivePanels] = useState({
    layers: true,
    properties: true,
    pages: true,
    designSystem: false
  })
  const [draggedElement, setDraggedElement] = useState<string | null>(null)

  useEffect(() => {
    const promptParam = searchParams.get('prompt')
    const techStackParam = searchParams.get('techStack') as 'html' | 'react'

    if (promptParam) setPrompt(promptParam)
    if (techStackParam && mockGeneratedCode[techStackParam]) {
      setTechStack(techStackParam)
      setGeneratedCode(mockGeneratedCode[techStackParam])

      // Initialize website state from generated code
      try {
        initializeWebsiteState(mockGeneratedCode[techStackParam])
      } catch (error) {
        console.error('Error initializing website state:', error)
        // Fallback initialization
        const fallbackState = {
          ...websiteState,
          pages: [{
            id: 'page-home',
            name: 'Home',
            title: 'Home - Your Website',
            slug: '/',
            metaDescription: 'Welcome to our website',
            elements: [],
            template: 'home' as const
          }],
          currentPageId: 'page-home'
        }
        setWebsiteState(fallbackState)
        setHistory([fallbackState])
        setHistoryIndex(0)
      }
    } else {
      // Initialize with default state if no tech stack is provided
      const defaultState = {
        ...websiteState,
        pages: [{
          id: 'page-home',
          name: 'Home',
          title: 'Home - Your Website',
          slug: '/',
          metaDescription: 'Welcome to our website',
          elements: [],
          template: 'home' as const
        }],
        currentPageId: 'page-home'
      }
      setWebsiteState(defaultState)
      setHistory([defaultState])
      setHistoryIndex(0)
    }
  }, [searchParams])

  const initializeWebsiteState = (code: string) => {
    // Parse the generated code and create initial website state with pages
    const initialElements: WebsiteElement[] = [
      {
        id: 'header-1',
        type: 'text',
        content: 'Welcome to Our Website',
        styles: { fontSize: '3rem', fontWeight: 'bold', color: 'white' },
        position: { x: 0, y: 0, width: 100, height: 20 },
        animations: { type: 'fade', duration: 1000, delay: 0 },
        borders: { width: '0px', style: 'solid', color: 'transparent', radius: '0px' },
        shadows: { x: '0px', y: '0px', blur: '0px', spread: '0px', color: 'transparent' }
      },
      {
        id: 'subtitle-1',
        type: 'text',
        content: 'Beautiful, modern design created by AI',
        styles: { fontSize: '1.2rem', opacity: '0.9', color: 'white' },
        position: { x: 0, y: 20, width: 100, height: 10 },
        animations: { type: 'fade', duration: 1000, delay: 200 },
        borders: { width: '0px', style: 'solid', color: 'transparent', radius: '0px' },
        shadows: { x: '0px', y: '0px', blur: '0px', spread: '0px', color: 'transparent' }
      },
      {
        id: 'button-1',
        type: 'button',
        content: 'Get Started',
        styles: { backgroundColor: 'white', color: '#667eea', padding: '1rem 2rem', borderRadius: '50px' },
        position: { x: 45, y: 35, width: 10, height: 5 },
        animations: { type: 'scale', duration: 800, delay: 400 },
        borders: { width: '0px', style: 'solid', color: 'transparent', radius: '50px' },
        shadows: { x: '0px', y: '4px', blur: '12px', spread: '0px', color: 'rgba(0,0,0,0.1)' }
      }
    ]

    const homePage: WebsitePage = {
      id: 'page-home',
      name: 'Home',
      title: 'Home - Your Website',
      slug: '/',
      metaDescription: 'Welcome to our beautiful website created with AI',
      elements: initialElements,
      template: 'home'
    }

    const newState = {
      ...websiteState,
      pages: [homePage],
      currentPageId: 'page-home'
    }
    setWebsiteState(newState)
    setHistory([newState])
    setHistoryIndex(0)
  }

  const saveToHistory = (newState: WebsiteState) => {
    const newHistory = history.slice(0, historyIndex + 1)
    newHistory.push(newState)
    setHistory(newHistory)
    setHistoryIndex(newHistory.length - 1)
  }

  const undo = () => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1)
      setWebsiteState(history[historyIndex - 1])
    }
  }

  const redo = () => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(historyIndex + 1)
      setWebsiteState(history[historyIndex + 1])
    }
  }

  const handleEdit = async (command: string) => {
    setIsEditProcessing(true)

    // Simulate edit processing
    setTimeout(() => {
      // In a real app, this would send the command to the backend
      console.log('Applying edit:', command)
      setIsEditProcessing(false)
    }, 2000)
  }

  const getCurrentPage = () => {
    return websiteState.pages.find(page => page.id === websiteState.currentPageId)
  }

  const updateElement = (elementId: string, updates: Partial<WebsiteElement>) => {
    const newState = {
      ...websiteState,
      pages: websiteState.pages.map(page =>
        page.id === websiteState.currentPageId
          ? {
              ...page,
              elements: page.elements.map(el =>
                el.id === elementId ? { ...el, ...updates } : el
              )
            }
          : page
      )
    }
    setWebsiteState(newState)
    saveToHistory(newState)
  }

  const duplicateElement = (elementId: string) => {
    const currentPage = getCurrentPage()
    if (!currentPage) return

    const element = currentPage.elements.find(el => el.id === elementId)
    if (!element) return

    const newElement = {
      ...element,
      id: `${elementId}-copy-${Date.now()}`,
      position: {
        ...element.position,
        x: element.position.x + 20,
        y: element.position.y + 20
      }
    }

    const newState = {
      ...websiteState,
      pages: websiteState.pages.map(page =>
        page.id === websiteState.currentPageId
          ? { ...page, elements: [...page.elements, newElement] }
          : page
      )
    }
    setWebsiteState(newState)
    saveToHistory(newState)
  }

  const deleteElement = (elementId: string) => {
    const newState = {
      ...websiteState,
      pages: websiteState.pages.map(page =>
        page.id === websiteState.currentPageId
          ? { ...page, elements: page.elements.filter(el => el.id !== elementId) }
          : page
      )
    }
    setWebsiteState(newState)
    saveToHistory(newState)
    if (selectedElement === elementId) {
      setSelectedElement(null)
    }
  }

  const addNewPage = (template: WebsitePage['template'] = 'custom') => {
    const pageId = `page-${Date.now()}`
    const newPage: WebsitePage = {
      id: pageId,
      name: `New Page`,
      title: 'New Page - Your Website',
      slug: `/page-${Date.now()}`,
      metaDescription: 'A new page on your website',
      elements: [],
      template
    }

    const newState = {
      ...websiteState,
      pages: [...websiteState.pages, newPage],
      currentPageId: pageId
    }
    setWebsiteState(newState)
    saveToHistory(newState)
  }

  const switchPage = (pageId: string) => {
    setWebsiteState(prev => ({ ...prev, currentPageId: pageId }))
    setSelectedElement(null)
    setSelectedElements([])
  }

  const updateGlobalStyles = (updates: Partial<WebsiteState['globalStyles']>) => {
    const newState = {
      ...websiteState,
      globalStyles: { ...websiteState.globalStyles, ...updates }
    }
    setWebsiteState(newState)
    saveToHistory(newState)
  }

  const updateLayout = (updates: Partial<WebsiteState['layout']>) => {
    const newState = {
      ...websiteState,
      layout: { ...websiteState.layout, ...updates }
    }
    setWebsiteState(newState)
    saveToHistory(newState)
  }

  const togglePanel = (panel: keyof typeof activePanels) => {
    setActivePanels(prev => ({ ...prev, [panel]: !prev[panel] }))
  }

  const handleShare = () => {
    const shareUrl = `${window.location.origin}/share/${projectId}`
    navigator.clipboard.writeText(shareUrl)
    // Show toast notification in a real app
    alert('Share link copied to clipboard!')
  }

  const handleExport = () => {
    router.push(`/project/export?id=${projectId}&techStack=${techStack}`)
  }

  const goBack = () => {
    router.back()
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-stone-50 flex flex-col relative overflow-hidden">
      {/* Enhanced Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-200/15 to-purple-200/15 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-purple-200/15 to-pink-200/15 rounded-full blur-3xl"></div>
        <div className="absolute top-1/3 left-1/3 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-cyan-200/8 to-blue-200/8 rounded-full blur-3xl"></div>
        <div className="absolute top-20 right-20 w-32 h-32 bg-gradient-to-br from-indigo-200/20 to-blue-200/20 rounded-full blur-2xl"></div>
        <div className="absolute bottom-20 left-20 w-40 h-40 bg-gradient-to-br from-violet-200/15 to-purple-200/15 rounded-full blur-2xl"></div>
      </div>

      {/* Top Header Bar */}
      <div className="bg-white/70 backdrop-blur-md border-b border-white/20 px-4 py-3 flex items-center justify-between shadow-lg shadow-slate-200/50 relative z-10">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={goBack}
            size="sm"
            className="bg-white/70 backdrop-blur-sm border border-white/30 shadow-lg shadow-slate-200/50"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>

          <div className="flex items-center space-x-2">
            <div className="inline-flex items-center px-3 py-1 rounded-full bg-white/60 backdrop-blur-sm border border-white/30 shadow-md shadow-slate-200/30">
              <Sparkles className="h-3 w-3 text-slate-600 mr-1" />
              <span className="text-xs font-medium text-slate-700">SiteForge Studio</span>
            </div>
            <h1 className="text-lg font-bold text-slate-900">Website Editor</h1>
          </div>
        </div>

        {/* Center Toolbar */}
        <EditorToolbar
          onUndo={undo}
          onRedo={redo}
          canUndo={historyIndex > 0}
          canRedo={historyIndex < history.length - 1}
          selectedElement={selectedElement}
          onDuplicate={() => selectedElement && duplicateElement(selectedElement)}
          onDelete={() => selectedElement && deleteElement(selectedElement)}
          gridEnabled={websiteState.grid.enabled}
          onToggleGrid={() => setWebsiteState(prev => ({
            ...prev,
            grid: { ...prev.grid, enabled: !prev.grid.enabled }
          }))}
        />

        <div className="flex items-center space-x-2">
          {/* Panel Toggle Buttons */}
          <div className="flex items-center space-x-1 bg-white/60 backdrop-blur-sm border border-white/30 rounded-lg p-1 shadow-md shadow-slate-200/30">
            <Button
              variant={activePanels.pages ? "default" : "ghost"}
              size="sm"
              onClick={() => togglePanel('pages')}
              className={`p-2 ${activePanels.pages ? 'bg-slate-900/80 backdrop-blur-sm' : 'hover:bg-white/50'}`}
            >
              <FileText className="h-4 w-4" />
            </Button>
            <Button
              variant={activePanels.layers ? "default" : "ghost"}
              size="sm"
              onClick={() => togglePanel('layers')}
              className={`p-2 ${activePanels.layers ? 'bg-slate-900/80 backdrop-blur-sm' : 'hover:bg-white/50'}`}
            >
              <Layers className="h-4 w-4" />
            </Button>
            <Button
              variant={activePanels.designSystem ? "default" : "ghost"}
              size="sm"
              onClick={() => togglePanel('designSystem')}
              className={`p-2 ${activePanels.designSystem ? 'bg-slate-900/80 backdrop-blur-sm' : 'hover:bg-white/50'}`}
            >
              <Palette className="h-4 w-4" />
            </Button>
            <Button
              variant={activePanels.properties ? "default" : "ghost"}
              size="sm"
              onClick={() => togglePanel('properties')}
              className={`p-2 ${activePanels.properties ? 'bg-slate-900/80 backdrop-blur-sm' : 'hover:bg-white/50'}`}
            >
              <Settings className="h-4 w-4" />
            </Button>
          </div>

          <Button variant="outline" onClick={handleShare} size="sm" className="bg-white/70 backdrop-blur-sm border-white/30 shadow-md shadow-slate-200/30">
            <Share2 className="mr-2 h-4 w-4" />
            Share
          </Button>
          <Button onClick={handleExport} size="sm" className="bg-slate-900/80 backdrop-blur-sm text-white border border-slate-700/50 shadow-lg shadow-slate-900/30">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Main Editor Layout - Full Width */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left Sidebar - Pages */}
        {activePanels.pages && (
          <div className="w-64 bg-white/70 backdrop-blur-md border-r border-white/20 flex-shrink-0 shadow-lg shadow-slate-200/30">
            <PageManager
              pages={websiteState.pages}
              currentPageId={websiteState.currentPageId}
              onSwitchPage={switchPage}
              onAddPage={addNewPage}
              onUpdatePage={(pageId, updates) => {
                const newState = {
                  ...websiteState,
                  pages: websiteState.pages.map(page =>
                    page.id === pageId ? { ...page, ...updates } : page
                  )
                }
                setWebsiteState(newState)
                saveToHistory(newState)
              }}
            />
          </div>
        )}

        {/* Left Sidebar - Layers */}
        {activePanels.layers && (
          <div className="w-64 bg-white/70 backdrop-blur-md border-r border-white/20 flex-shrink-0 shadow-lg shadow-slate-200/30">
            <LayersPanel
              elements={getCurrentPage()?.elements || []}
              selectedElement={selectedElement}
              selectedElements={selectedElements}
              onSelectElement={setSelectedElement}
              onSelectMultiple={setSelectedElements}
              onUpdateElement={updateElement}
              onDuplicateElement={duplicateElement}
              onDeleteElement={deleteElement}
              onReorderElements={(elementIds) => {
                const currentPage = getCurrentPage()
                if (!currentPage) return

                const reorderedElements = elementIds.map(id =>
                  currentPage.elements.find(el => el.id === id)!
                ).filter(Boolean)

                const newState = {
                  ...websiteState,
                  pages: websiteState.pages.map(page =>
                    page.id === websiteState.currentPageId
                      ? { ...page, elements: reorderedElements }
                      : page
                  )
                }
                setWebsiteState(newState)
                saveToHistory(newState)
              }}
            />
          </div>
        )}

        {/* Main Preview Area - Full Width */}
        <div className="flex-1 flex flex-col bg-slate-100/50 backdrop-blur-sm">
          {websiteState && websiteState.pages && websiteState.pages.length > 0 ? (
            <EnhancedWebsitePreview
              websiteState={websiteState}
              currentPage={getCurrentPage()}
              generatedCode={generatedCode}
              techStack={techStack}
              selectedElement={selectedElement}
              selectedElements={selectedElements}
              onSelectElement={setSelectedElement}
              onSelectMultiple={setSelectedElements}
              onUpdateElement={updateElement}
              gridEnabled={websiteState.grid?.enabled || false}
              gridSize={websiteState.grid?.size || 20}
              snapToGrid={websiteState.grid?.snap || true}
            />
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <div className="text-slate-500 mb-2">Initializing website...</div>
                <div className="w-6 h-6 border-2 border-slate-300 border-t-slate-600 rounded-full animate-spin mx-auto"></div>
              </div>
            </div>
          )}
        </div>

        {/* Right Sidebar - Design System */}
        {activePanels.designSystem && (
          <div className="w-64 bg-white/70 backdrop-blur-md border-l border-white/20 flex-shrink-0 shadow-lg shadow-slate-200/30">
            <DesignSystemPanel
              designSystem={websiteState.designSystem}
              onUpdateDesignSystem={(updates) => {
                const newState = {
                  ...websiteState,
                  designSystem: { ...websiteState.designSystem, ...updates }
                }
                setWebsiteState(newState)
                saveToHistory(newState)
              }}
              onApplyComponent={(component) => {
                // Add component to current page
                const currentPage = getCurrentPage()
                if (!currentPage) return

                const newElement: WebsiteElement = {
                  ...component,
                  id: `element-${Date.now()}`,
                  position: { x: 50, y: 50, width: 200, height: 100 }
                }

                const newState = {
                  ...websiteState,
                  pages: websiteState.pages.map(page =>
                    page.id === websiteState.currentPageId
                      ? { ...page, elements: [...page.elements, newElement] }
                      : page
                  )
                }
                setWebsiteState(newState)
                saveToHistory(newState)
              }}
            />
          </div>
        )}

        {/* Right Sidebar - Properties */}
        {activePanels.properties && (
          <div className="w-80 bg-white/70 backdrop-blur-md border-l border-white/20 flex-shrink-0 shadow-lg shadow-slate-200/30">
            <PropertiesPanel
              selectedElement={selectedElement ? getCurrentPage()?.elements.find(el => el.id === selectedElement) : null}
              websiteState={websiteState}
              onUpdateElement={updateElement}
              onUpdateGlobalStyles={updateGlobalStyles}
              onUpdateLayout={updateLayout}
              onApplyEdit={handleEdit}
              isProcessing={isEditProcessing}
            />
          </div>
        )}
      </div>
    </div>
  )
}

export default function PreviewPage() {
  return (
    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
      <PreviewPageContent />
    </Suspense>
  )
}
