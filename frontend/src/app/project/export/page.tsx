"use client"

import { useState, useEffect, Suspense } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Download,
  CreditCard,
  Check,
  FileArchive,
  ArrowLeft,
  Package,
  Globe,
  Zap,
  Shield,
  Code
} from "lucide-react"
import { ExportOptions } from "@/types"

const exportOptions: ExportOptions[] = [
  {
    format: 'zip',
    price: 29
  },
  {
    format: 'netlify',
    price: 39
  },
  {
    format: 'vercel',
    price: 39
  }
]

const exportFeatures = {
  zip: [
    "Complete source code",
    "All assets and images",
    "Setup instructions",
    "No hosting included"
  ],
  netlify: [
    "Complete source code",
    "All assets and images", 
    "Auto-deployed to Netlify",
    "Custom domain ready",
    "SSL certificate included",
    "Free hosting for 1 year"
  ],
  vercel: [
    "Complete source code",
    "All assets and images",
    "Auto-deployed to Vercel", 
    "Custom domain ready",
    "SSL certificate included",
    "Free hosting for 1 year"
  ]
}

function ExportPageContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [selectedOption, setSelectedOption] = useState<'zip' | 'netlify' | 'vercel'>('zip')
  const [isProcessing, setIsProcessing] = useState(false)
  const [projectId, setProjectId] = useState("")
  const [techStack, setTechStack] = useState<'html' | 'react'>('html')

  useEffect(() => {
    const id = searchParams.get('id')
    const stack = searchParams.get('techStack') as 'html' | 'react'
    
    if (id) setProjectId(id)
    if (stack) setTechStack(stack)
  }, [searchParams])

  const handlePurchase = async () => {
    setIsProcessing(true)
    
    // Simulate Stripe payment processing
    setTimeout(() => {
      if (selectedOption === 'zip') {
        // Simulate zip download
        const element = document.createElement('a')
        element.href = 'data:text/plain;charset=utf-8,// Your website code would be here'
        element.download = `website-${projectId}.zip`
        element.style.display = 'none'
        document.body.appendChild(element)
        element.click()
        document.body.removeChild(element)
      } else {
        // Simulate deployment
        window.open(`https://${projectId}.${selectedOption}.app`, '_blank')
      }
      
      setIsProcessing(false)
      router.push('/success')
    }, 3000)
  }

  const goBack = () => {
    router.back()
  }

  const getOptionIcon = (format: string) => {
    switch (format) {
      case 'zip': return FileArchive
      case 'netlify': return Globe
      case 'vercel': return Zap
      default: return Package
    }
  }

  const getOptionTitle = (format: string) => {
    switch (format) {
      case 'zip': return 'Download ZIP'
      case 'netlify': return 'Deploy to Netlify'
      case 'vercel': return 'Deploy to Vercel'
      default: return format
    }
  }

  const getOptionDescription = (format: string) => {
    switch (format) {
      case 'zip': return 'Download complete source code and host anywhere'
      case 'netlify': return 'Automatically deploy and host on Netlify\'s global CDN'
      case 'vercel': return 'Deploy instantly on Vercel\'s edge network'
      default: return ''
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-stone-50 relative">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-slate-200/30 to-stone-200/30 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-stone-200/30 to-slate-200/30 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 py-20 relative">
        <div className="max-w-5xl mx-auto">
          {/* Header */}
          <div className="text-center mb-16">
            <Button
              variant="outline"
              onClick={goBack}
              className="mb-8 bg-white border-slate-200 shadow-sm hover:shadow-md transition-all duration-300"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Preview
            </Button>

            <div className="inline-flex items-center px-4 py-2 rounded-full bg-white border border-slate-200 mb-8 shadow-sm">
              <Download className="h-4 w-4 text-slate-600 mr-2" />
              <span className="text-sm font-medium text-slate-700">Website Export</span>
              <span className="ml-2 px-2 py-1 text-xs bg-slate-100 text-slate-600 rounded-full">Ready</span>
            </div>

            <h1 className="text-5xl md:text-7xl font-bold text-slate-900 mb-6 leading-tight">
              Export Your
              <span className="block text-slate-700">
                Website
              </span>
            </h1>

            <p className="text-xl md:text-2xl text-slate-600 mb-12 leading-relaxed max-w-3xl mx-auto">
              Choose your preferred export method and get your website ready for the world
            </p>
          </div>

          {/* Project Info */}
          <Card className="mb-16 border-slate-200 shadow-sm">
            <CardHeader className="pb-4">
              <CardTitle className="text-xl text-slate-900">Project Details</CardTitle>
              <CardDescription className="text-slate-600">
                Your website is ready for export
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center p-4 bg-slate-50 rounded-lg">
                  <div className="w-12 h-12 bg-slate-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <Code className="h-6 w-6 text-slate-600" />
                  </div>
                  <span className="text-sm font-medium text-slate-700 block mb-1">Tech Stack</span>
                  <p className="text-slate-900 font-semibold">
                    {techStack === 'html' ? 'HTML/CSS/JS' : 'React + Tailwind'}
                  </p>
                </div>
                <div className="text-center p-4 bg-slate-50 rounded-lg">
                  <div className="w-12 h-12 bg-slate-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <Package className="h-6 w-6 text-slate-600" />
                  </div>
                  <span className="text-sm font-medium text-slate-700 block mb-1">Project ID</span>
                  <p className="text-slate-900 font-semibold text-sm">{projectId}</p>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <Check className="h-6 w-6 text-green-600" />
                  </div>
                  <span className="text-sm font-medium text-slate-700 block mb-1">Status</span>
                  <p className="text-green-600 font-semibold">Ready to Export</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Export Options Section */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-3 py-1 rounded-full bg-slate-100 text-slate-700 text-sm font-medium mb-6">
              <Package className="h-4 w-4 mr-2" />
              Export Options
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-slate-900 mb-6">
              Choose Your
              <span className="text-slate-700">
                Export Method
              </span>
            </h2>
            <p className="text-xl text-slate-600 max-w-2xl mx-auto">
              Select the perfect way to get your website into the world
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            {exportOptions.map((option) => {
              const Icon = getOptionIcon(option.format)
              const isSelected = selectedOption === option.format
              const features = exportFeatures[option.format]

              return (
                <Card
                  key={option.format}
                  className={`cursor-pointer transition-all duration-200 border-slate-200 shadow-sm hover:shadow-md ${
                    isSelected
                      ? 'ring-2 ring-slate-900 ring-offset-2'
                      : ''
                  }`}
                  onClick={() => setSelectedOption(option.format)}
                >
                  <CardHeader className="pb-4">
                    <div className="flex items-center justify-between mb-4">
                      <div className="w-12 h-12 bg-slate-100 rounded-lg flex items-center justify-center">
                        <Icon className="h-6 w-6 text-slate-600" />
                      </div>
                      <div className={`w-6 h-6 rounded-full border-2 ${
                        isSelected ? 'border-slate-500 bg-slate-500' : 'border-slate-300'
                      }`}>
                        {isSelected && (
                          <div className="w-full h-full rounded-full bg-slate-500 flex items-center justify-center">
                            <div className="w-2 h-2 bg-white rounded-full"></div>
                          </div>
                        )}
                      </div>
                    </div>
                    <CardTitle className="text-xl text-slate-900">{getOptionTitle(option.format)}</CardTitle>
                    <CardDescription className="text-slate-600">
                      {getOptionDescription(option.format)}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3 mb-6">
                      {features.map((feature, index) => (
                        <div key={index} className="flex items-center text-sm text-slate-600">
                          <Check className="h-4 w-4 mr-3 text-green-600" />
                          {feature}
                        </div>
                      ))}
                    </div>
                    <div className="text-center pt-4 border-t border-slate-100">
                      <span className="text-3xl font-bold text-slate-900">${option.price}</span>
                      <span className="text-slate-600 ml-2">one-time</span>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>

          {/* Payment Section */}
          <div className="bg-slate-900 rounded-2xl px-8 py-16 text-center text-white">
            <div className="max-w-2xl mx-auto">
              <div className="inline-flex items-center px-4 py-2 rounded-full bg-slate-800 border border-slate-700 mb-8">
                <CreditCard className="h-4 w-4 mr-2" />
                <span className="text-sm font-medium">Secure Checkout</span>
              </div>

              <h2 className="text-4xl md:text-5xl font-bold mb-6 leading-tight">
                Complete Your
                <span className="block text-slate-300">
                  Purchase
                </span>
              </h2>

              <p className="text-xl text-slate-300 mb-12 leading-relaxed">
                Secure payment powered by Stripe with SSL encryption
              </p>

              {/* Order Summary */}
              <div className="bg-slate-800 rounded-xl p-6 mb-8 text-left">
                <h4 className="font-semibold text-white mb-4 text-center">Order Summary</h4>
                <div className="flex justify-between items-center mb-3">
                  <span className="text-slate-300">{getOptionTitle(selectedOption)}</span>
                  <span className="font-semibold text-white">
                    ${exportOptions.find(o => o.format === selectedOption)?.price}
                  </span>
                </div>
                <div className="border-t border-slate-700 pt-3 flex justify-between items-center font-bold text-lg">
                  <span className="text-white">Total</span>
                  <span className="text-white">${exportOptions.find(o => o.format === selectedOption)?.price}</span>
                </div>
              </div>

              {/* Payment Form Placeholder */}
              <div className="border-2 border-dashed border-slate-700 rounded-xl p-8 mb-8">
                <Shield className="h-8 w-8 text-slate-400 mx-auto mb-3" />
                <p className="text-slate-300 mb-4">
                  Stripe payment form would be integrated here
                </p>
                <p className="text-sm text-slate-400">
                  Secure SSL encryption • No stored card details
                </p>
              </div>

              {/* Action Button */}
              <Button
                onClick={handlePurchase}
                disabled={isProcessing}
                size="lg"
                variant="secondary"
                className="w-full text-lg py-6 mb-6"
              >
                {isProcessing ? (
                  <>
                    <CreditCard className="mr-2 h-5 w-5 animate-pulse" />
                    Processing Payment...
                  </>
                ) : (
                  <>
                    Complete Purchase - ${exportOptions.find(o => o.format === selectedOption)?.price}
                    <CreditCard className="ml-2 h-5 w-5" />
                  </>
                )}
              </Button>

              {/* Terms */}
              <div className="flex flex-wrap justify-center items-center gap-6 text-sm text-slate-400">
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-slate-500 rounded-full mr-2"></div>
                  Secure payment
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-slate-500 rounded-full mr-2"></div>
                  SSL encrypted
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-slate-500 rounded-full mr-2"></div>
                  No hidden fees
                </div>
              </div>

              <p className="text-xs text-slate-400 text-center mt-6">
                By completing this purchase, you agree to our{' '}
                <a href="/terms" className="text-slate-300 hover:underline">Terms of Service</a>
                {' '}and{' '}
                <a href="/privacy" className="text-slate-300 hover:underline">Privacy Policy</a>
              </p>
            </div>
          </div>

          {/* Processing State */}
          {isProcessing && (
            <Card className="mt-8 border-0 shadow-lg">
              <CardContent className="pt-6">
                <div className="text-center">
                  <div className="mb-4">
                    <CreditCard className="h-8 w-8 text-blue-600 mx-auto animate-pulse" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Processing Your Order...
                  </h3>
                  <p className="text-gray-600 mb-4">
                    {selectedOption === 'zip' 
                      ? 'Preparing your download...'
                      : `Deploying to ${selectedOption}...`
                    }
                  </p>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-blue-600 h-2 rounded-full animate-pulse" style={{width: '75%'}}></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}

export default function ExportPage() {
  return (
    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
      <ExportPageContent />
    </Suspense>
  )
}
