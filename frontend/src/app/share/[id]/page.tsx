"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { WebsitePreview } from "@/components/features/website-preview"
import { CommentSystem } from "@/components/features/comment-system"
import { Button } from "@/components/ui/button"
import { 
  Eye, 
  MessageSquare, 
  ExternalLink, 
  Shield,
  Clock,
  User,
  Code
} from "lucide-react"
import { Comment } from "@/types"

// Mock project data
const mockProject = {
  id: 'project-123',
  name: 'Portfolio Website',
  prompt: 'A modern portfolio website for a graphic designer with a dark theme, image gallery, contact form, and smooth animations',
  techStack: 'react' as 'react' | 'html',
  generatedCode: `
    const App = () => {
      return (
        <div className="min-h-screen bg-gray-900 text-white">
          <header className="bg-gradient-to-r from-purple-600 to-blue-600 py-20 px-8 text-center">
            <h1 className="text-5xl font-bold mb-4"><PERSON></h1>
            <p className="text-xl opacity-90 mb-8">Graphic Designer & Creative Professional</p>
            <button className="bg-white text-purple-600 px-8 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors">
              View My Work
            </button>
          </header>
          
          <section className="py-16 px-8 max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-12">Featured Projects</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <div className="bg-gray-800 rounded-lg overflow-hidden hover:transform hover:scale-105 transition-all">
                <div className="h-48 bg-gradient-to-br from-purple-500 to-pink-500"></div>
                <div className="p-6">
                  <h3 className="text-xl font-bold mb-2">Brand Identity</h3>
                  <p className="text-gray-400">Complete brand redesign for tech startup</p>
                </div>
              </div>
              <div className="bg-gray-800 rounded-lg overflow-hidden hover:transform hover:scale-105 transition-all">
                <div className="h-48 bg-gradient-to-br from-blue-500 to-teal-500"></div>
                <div className="p-6">
                  <h3 className="text-xl font-bold mb-2">Web Design</h3>
                  <p className="text-gray-400">Modern e-commerce platform design</p>
                </div>
              </div>
              <div className="bg-gray-800 rounded-lg overflow-hidden hover:transform hover:scale-105 transition-all">
                <div className="h-48 bg-gradient-to-br from-green-500 to-yellow-500"></div>
                <div className="p-6">
                  <h3 className="text-xl font-bold mb-2">Print Design</h3>
                  <p className="text-gray-400">Magazine layout and typography</p>
                </div>
              </div>
            </div>
          </section>
          
          <section className="bg-gray-800 py-16 px-8">
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="text-3xl font-bold mb-8">Let's Work Together</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <input type="text" placeholder="Your Name" className="p-4 bg-gray-700 rounded-lg text-white placeholder-gray-400" />
                <input type="email" placeholder="Your Email" className="p-4 bg-gray-700 rounded-lg text-white placeholder-gray-400" />
              </div>
              <textarea placeholder="Tell me about your project..." className="w-full p-4 bg-gray-700 rounded-lg text-white placeholder-gray-400 mt-4 h-32"></textarea>
              <button className="bg-purple-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-purple-700 transition-colors mt-6">
                Send Message
              </button>
            </div>
          </section>
        </div>
      );
    };
    
    ReactDOM.render(<App />, document.getElementById('root'));
  `,
  status: 'ready' as const,
  createdAt: new Date('2024-01-15'),
  updatedAt: new Date('2024-01-15')
}

const mockComments: Comment[] = [
  {
    id: '1',
    projectId: 'project-123',
    author: 'Sarah Johnson',
    content: 'I love the dark theme and the gradient effects! The portfolio cards look very professional. Could we maybe make the contact form a bit more prominent?',
    createdAt: new Date('2024-01-15T10:30:00')
  },
  {
    id: '2',
    projectId: 'project-123',
    author: 'Mike Chen',
    content: 'The hover effects on the project cards are really nice. One suggestion: maybe we could add some animation when the page loads?',
    createdAt: new Date('2024-01-15T14:45:00')
  },
  {
    id: '3',
    projectId: 'project-123',
    author: 'Emily Davis',
    content: 'This looks fantastic! The color scheme is perfect. I think we should keep this design as is.',
    createdAt: new Date('2024-01-15T16:20:00')
  }
]

export default function SharePage() {
  const params = useParams()
  const projectId = params.id as string
  const [comments, setComments] = useState<Comment[]>(mockComments)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Simulate loading project data
    setTimeout(() => {
      setIsLoading(false)
    }, 1000)
  }, [projectId])

  const handleAddComment = async (newComment: Omit<Comment, 'id' | 'createdAt'>) => {
    const comment: Comment = {
      ...newComment,
      id: `comment-${Date.now()}`,
      createdAt: new Date()
    }
    
    setComments(prev => [comment, ...prev])
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading project...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-stone-50 relative">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-slate-200/30 to-stone-200/30 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-stone-200/30 to-slate-200/30 rounded-full blur-3xl"></div>
      </div>

      {/* Header */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200 relative">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-4 shadow-lg">
                <Eye className="h-6 w-6 text-white" />
              </div>
              <div>
                <div className="inline-flex items-center px-3 py-1 rounded-full bg-blue-100 text-blue-700 text-sm font-medium mb-2">
                  <Shield className="h-4 w-4 mr-2" />
                  Secure Preview
                </div>
                <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-1">Website Preview</h1>
                <p className="text-lg text-gray-600">Shared for review and feedback</p>
              </div>
            </div>
            
            <div className="hidden md:flex items-center space-x-2 text-sm text-gray-500 bg-gray-100 px-4 py-2 rounded-lg">
              <Shield className="h-4 w-4" />
              <span>Secure preview link</span>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {/* Project Info */}
        <Card className="mb-8 border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>{mockProject.name}</span>
              <div className="flex items-center space-x-4 text-sm text-gray-500">
                <div className="flex items-center">
                  <Clock className="h-4 w-4 mr-1" />
                  Created {mockProject.createdAt.toLocaleDateString()}
                </div>
                <div className="flex items-center">
                  <Code className="h-4 w-4 mr-1" />
                  {mockProject.techStack === 'html' ? 'HTML/CSS/JS' : 'React + Tailwind'}
                </div>
              </div>
            </CardTitle>
            <CardDescription>
              "{mockProject.prompt}"
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-6 text-sm text-gray-600">
                <div className="flex items-center">
                  <MessageSquare className="h-4 w-4 mr-1" />
                  {comments.length} comment{comments.length !== 1 ? 's' : ''}
                </div>
                <div className="flex items-center">
                  <User className="h-4 w-4 mr-1" />
                  Open for feedback
                </div>
              </div>
              
              <Button variant="outline" size="sm">
                <ExternalLink className="h-4 w-4 mr-2" />
                Open in New Tab
              </Button>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Preview Section */}
          <div className="lg:col-span-2">
            <WebsitePreview
              generatedCode={mockProject.generatedCode}
              techStack={mockProject.techStack}
            />
          </div>

          {/* Comments Section */}
          <div className="lg:col-span-1">
            <CommentSystem
              comments={comments}
              onAddComment={handleAddComment}
            />
          </div>
        </div>

        {/* Instructions */}
        <Card className="mt-8 border-0 shadow-lg bg-blue-50 border-blue-200">
          <CardHeader>
            <CardTitle className="text-blue-900">How to Provide Feedback</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm text-blue-800">
              <div className="flex items-start">
                <div className="w-6 h-6 bg-blue-200 rounded-full flex items-center justify-center mr-3 mt-0.5">
                  <span className="text-xs font-bold">1</span>
                </div>
                <p>Review the website design and functionality in the preview above</p>
              </div>
              <div className="flex items-start">
                <div className="w-6 h-6 bg-blue-200 rounded-full flex items-center justify-center mr-3 mt-0.5">
                  <span className="text-xs font-bold">2</span>
                </div>
                <p>Use the comment form to share your thoughts, suggestions, or questions</p>
              </div>
              <div className="flex items-start">
                <div className="w-6 h-6 bg-blue-200 rounded-full flex items-center justify-center mr-3 mt-0.5">
                  <span className="text-xs font-bold">3</span>
                </div>
                <p>Be specific about what you like and what could be improved</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
