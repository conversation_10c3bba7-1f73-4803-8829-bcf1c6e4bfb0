"use client"

import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>Right, Zap, Eye, Download, Code, Palette, MessageSquare, <PERSON>rkles, <PERSON>, Users, Clock } from "lucide-react"

export default function Home() {
  return (
    <div className="bg-gradient-to-br from-slate-50 via-white to-stone-50 relative overflow-hidden">
      {/* Enhanced Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-200/20 to-purple-200/20 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-purple-200/20 to-pink-200/20 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-cyan-200/10 to-blue-200/10 rounded-full blur-3xl"></div>
        <div className="absolute top-20 left-20 w-32 h-32 bg-gradient-to-br from-indigo-200/30 to-blue-200/30 rounded-full blur-2xl"></div>
        <div className="absolute bottom-20 right-20 w-40 h-40 bg-gradient-to-br from-violet-200/25 to-purple-200/25 rounded-full blur-2xl"></div>
      </div>

      {/* Hero Section */}
      <section className="container mx-auto px-4 py-20 relative">
        <div className="text-center max-w-5xl mx-auto">
          {/* Glassmorphism Badge */}
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-white/70 backdrop-blur-md border border-white/20 mb-8 shadow-lg shadow-slate-200/50">
            <Sparkles className="h-4 w-4 text-slate-600 mr-2" />
            <span className="text-sm font-medium text-slate-700">AI-Powered Website Generation</span>
            <span className="ml-2 px-2 py-1 text-xs bg-white/60 backdrop-blur-sm text-slate-600 rounded-full border border-white/30">Beta</span>
          </div>

          <h1 className="text-5xl md:text-7xl font-bold text-slate-900 mb-6 leading-tight">
            Generate Beautiful 
            <span className="block text-slate-700">
              Websites from Text
            </span>
          </h1>
          
          <p className="text-xl md:text-2xl text-slate-600 mb-12 leading-relaxed max-w-3xl mx-auto">
            Transform your ideas into stunning websites instantly. Just describe what you want, 
            upload a screenshot for reference, and watch AI create your perfect site.
          </p>

          {/* Glassmorphism Stats Row */}
          <div className="flex flex-wrap justify-center gap-8 mb-12 text-sm text-slate-600">
            <div className="flex items-center px-4 py-3 rounded-xl bg-white/60 backdrop-blur-sm border border-white/30 shadow-lg shadow-slate-200/30">
              <div className="w-8 h-8 bg-white/70 backdrop-blur-sm rounded-full flex items-center justify-center mr-3 border border-white/40">
                <Clock className="h-4 w-4 text-slate-600" />
              </div>
              <span><strong className="text-slate-900">Under 20s</strong> generation time</span>
            </div>
            <div className="flex items-center px-4 py-3 rounded-xl bg-white/60 backdrop-blur-sm border border-white/30 shadow-lg shadow-slate-200/30">
              <div className="w-8 h-8 bg-white/70 backdrop-blur-sm rounded-full flex items-center justify-center mr-3 border border-white/40">
                <Users className="h-4 w-4 text-slate-600" />
              </div>
              <span><strong className="text-slate-900">1000+</strong> websites created</span>
            </div>
            <div className="flex items-center px-4 py-3 rounded-xl bg-white/60 backdrop-blur-sm border border-white/30 shadow-lg shadow-slate-200/30">
              <div className="w-8 h-8 bg-white/70 backdrop-blur-sm rounded-full flex items-center justify-center mr-3 border border-white/40">
                <Star className="h-4 w-4 text-slate-600" />
              </div>
              <span><strong className="text-slate-900">4.9/5</strong> user rating</span>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/create">
              <Button size="lg" className="text-lg px-10 py-6">
                Start Creating Free <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Link href="/examples">
              <Button variant="outline" size="lg" className="text-lg px-10 py-6">
                View Examples
              </Button>
            </Link>
          </div>

          {/* Glassmorphism Trust indicators */}
          <div className="mt-16 flex flex-wrap justify-center items-center gap-8 opacity-80">
            <div className="text-sm text-slate-500">Trusted by developers at</div>
            <div className="flex items-center space-x-4">
              <div className="px-4 py-2 bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg text-sm font-medium text-slate-600 shadow-md shadow-slate-200/30">Startup</div>
              <div className="px-4 py-2 bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg text-sm font-medium text-slate-600 shadow-md shadow-slate-200/30">Agency</div>
              <div className="px-4 py-2 bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg text-sm font-medium text-slate-600 shadow-md shadow-slate-200/30">Enterprise</div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="container mx-auto px-4 py-20 relative">
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-white/60 backdrop-blur-sm border border-white/30 text-slate-700 text-sm font-medium mb-6 shadow-lg shadow-slate-200/30">
            <Sparkles className="h-4 w-4 mr-2" />
            Powerful Features
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-slate-900 mb-6">
            Everything You Need to Build 
            <span className="text-slate-700">
              Amazing Sites
            </span>
          </h2>
          <p className="text-xl text-slate-600 max-w-2xl mx-auto">
            From concept to deployment in minutes, not days
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <Card className="bg-white/60 backdrop-blur-sm border border-white/30 shadow-xl shadow-slate-200/40">
            <CardHeader className="pb-4">
              <div className="w-12 h-12 bg-white/70 backdrop-blur-sm border border-white/40 rounded-lg flex items-center justify-center mb-4 shadow-md shadow-slate-200/30">
                <Zap className="h-6 w-6 text-slate-600" />
              </div>
              <CardTitle className="text-xl text-slate-900">AI-Powered Generation</CardTitle>
              <CardDescription className="text-slate-600">
                Describe your vision in plain English and get a fully functional website
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="bg-white/60 backdrop-blur-sm border border-white/30 shadow-xl shadow-slate-200/40">
            <CardHeader className="pb-4">
              <div className="w-12 h-12 bg-white/70 backdrop-blur-sm border border-white/40 rounded-lg flex items-center justify-center mb-4 shadow-md shadow-slate-200/30">
                <Code className="h-6 w-6 text-slate-600" />
              </div>
              <CardTitle className="text-xl text-slate-900">Multiple Tech Stacks</CardTitle>
              <CardDescription className="text-slate-600">
                Choose between HTML/CSS/JS or React with Tailwind CSS for your project
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="bg-white/60 backdrop-blur-sm border border-white/30 shadow-xl shadow-slate-200/40">
            <CardHeader className="pb-4">
              <div className="w-12 h-12 bg-white/70 backdrop-blur-sm border border-white/40 rounded-lg flex items-center justify-center mb-4 shadow-md shadow-slate-200/30">
                <Eye className="h-6 w-6 text-slate-600" />
              </div>
              <CardTitle className="text-xl text-slate-900">Live Preview</CardTitle>
              <CardDescription className="text-slate-600">
                See your website in action with our secure sandbox environment
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="bg-white/60 backdrop-blur-sm border border-white/30 shadow-xl shadow-slate-200/40">
            <CardHeader className="pb-4">
              <div className="w-12 h-12 bg-white/70 backdrop-blur-sm border border-white/40 rounded-lg flex items-center justify-center mb-4 shadow-md shadow-slate-200/30">
                <Palette className="h-6 w-6 text-slate-600" />
              </div>
              <CardTitle className="text-xl text-slate-900">Natural Language Edits</CardTitle>
              <CardDescription className="text-slate-600">
                Make changes by simply describing what you want to modify
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="bg-white/60 backdrop-blur-sm border border-white/30 shadow-xl shadow-slate-200/40">
            <CardHeader className="pb-4">
              <div className="w-12 h-12 bg-white/70 backdrop-blur-sm border border-white/40 rounded-lg flex items-center justify-center mb-4 shadow-md shadow-slate-200/30">
                <MessageSquare className="h-6 w-6 text-slate-600" />
              </div>
              <CardTitle className="text-xl text-slate-900">Client Collaboration</CardTitle>
              <CardDescription className="text-slate-600">
                Share secure links with clients for feedback and approval
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="bg-white/60 backdrop-blur-sm border border-white/30 shadow-xl shadow-slate-200/40">
            <CardHeader className="pb-4">
              <div className="w-12 h-12 bg-white/70 backdrop-blur-sm border border-white/40 rounded-lg flex items-center justify-center mb-4 shadow-md shadow-slate-200/30">
                <Download className="h-6 w-6 text-slate-600" />
              </div>
              <CardTitle className="text-xl text-slate-900">Easy Export</CardTitle>
              <CardDescription className="text-slate-600">
                Download your code or deploy directly to Netlify/Vercel
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="bg-slate-50 py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-white/60 backdrop-blur-sm border border-white/30 text-slate-700 text-sm font-medium mb-6 shadow-lg shadow-slate-200/30">
              <Sparkles className="h-4 w-4 mr-2" />
              Simple Process
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-slate-900 mb-6">
              How It Works
            </h2>
            <p className="text-xl text-slate-600 max-w-2xl mx-auto">
              From idea to website in four simple steps
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-slate-900/90 backdrop-blur-sm text-white rounded-xl flex items-center justify-center mx-auto mb-6 text-xl font-semibold shadow-xl shadow-slate-900/30 border border-slate-700/50">
                1
              </div>
              <h3 className="text-xl font-semibold mb-3 text-slate-900">Describe Your Vision</h3>
              <p className="text-slate-600 leading-relaxed">
                Tell us what kind of website you want or upload a screenshot for reference
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-slate-900/90 backdrop-blur-sm text-white rounded-xl flex items-center justify-center mx-auto mb-6 text-xl font-semibold shadow-xl shadow-slate-900/30 border border-slate-700/50">
                2
              </div>
              <h3 className="text-xl font-semibold mb-3 text-slate-900">Choose Tech Stack</h3>
              <p className="text-slate-600 leading-relaxed">
                Select your preferred technology stack and framework
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-slate-900/90 backdrop-blur-sm text-white rounded-xl flex items-center justify-center mx-auto mb-6 text-xl font-semibold shadow-xl shadow-slate-900/30 border border-slate-700/50">
                3
              </div>
              <h3 className="text-xl font-semibold mb-3 text-slate-900">AI Generation</h3>
              <p className="text-slate-600 leading-relaxed">
                Our AI creates your website with pixel-perfect design and clean code
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-slate-900/90 backdrop-blur-sm text-white rounded-xl flex items-center justify-center mx-auto mb-6 text-xl font-semibold shadow-xl shadow-slate-900/30 border border-slate-700/50">
                4
              </div>
              <h3 className="text-xl font-semibold mb-3 text-slate-900">Preview & Export</h3>
              <p className="text-slate-600 leading-relaxed">
                Review, make changes, and export your finished website
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="container mx-auto px-4 py-20">
        <div className="bg-slate-900 rounded-2xl px-8 py-16 text-center text-white">
          <div className="max-w-3xl mx-auto">
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-slate-800/70 backdrop-blur-sm border border-slate-700/50 mb-8 shadow-lg shadow-slate-900/30">
              <Star className="h-4 w-4 mr-2" />
              <span className="text-sm font-medium">Join 1000+ Happy Users</span>
            </div>
            
            <h2 className="text-4xl md:text-5xl font-bold mb-6 leading-tight">
              Ready to Build Your 
              <span className="block text-slate-300">
                Dream Website?
              </span>
            </h2>
            
            <p className="text-xl text-slate-300 mb-12 leading-relaxed">
              Join thousands of developers and designers who trust AI SiteForge to bring their visions to life
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/create">
                <Button size="lg" variant="secondary" className="text-lg px-10 py-6">
                  Get Started for Free <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link href="/examples">
                <Button size="lg" variant="outline" className="text-lg px-10 py-6 border-slate-600 text-slate-300 hover:bg-slate-800">
                  View Examples
                </Button>
              </Link>
            </div>
            
            <div className="mt-12 flex flex-wrap justify-center items-center gap-6 text-sm text-slate-400">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-slate-500 rounded-full mr-2"></div>
                No credit card required
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-slate-500 rounded-full mr-2"></div>
                Free to try
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-slate-500 rounded-full mr-2"></div>
                Export when ready
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}