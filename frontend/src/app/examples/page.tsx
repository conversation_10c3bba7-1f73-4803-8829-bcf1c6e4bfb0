"use client"

import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Eye, ArrowRight, Code, Palette, Briefcase, ShoppingCart, Coffee, Heart } from "lucide-react"

const examples = [
  {
    id: 1,
    title: "Modern Portfolio",
    description: "Clean and minimal portfolio website for creative professionals",
    category: "Portfolio",
    techStack: "React + Tailwind",
    features: ["Dark theme", "Smooth animations", "Project gallery", "Contact form"],
    icon: Briefcase,
    color: "blue",
    image: "/api/placeholder/400/300"
  },
  {
    id: 2,
    title: "Restaurant Website",
    description: "Warm and inviting restaurant site with online reservations",
    category: "Business",
    techStack: "HTML/CSS/JS",
    features: ["Menu showcase", "Location map", "Reservation system", "Photo gallery"],
    icon: Coffee,
    color: "orange",
    image: "/api/placeholder/400/300"
  },
  {
    id: 3,
    title: "SaaS Landing Page",
    description: "High-converting landing page for software products",
    category: "SaaS",
    techStack: "React + Tailwind",
    features: ["Pricing tiers", "Feature highlights", "Customer testimonials", "CTA sections"],
    icon: Code,
    color: "purple",
    image: "/api/placeholder/400/300"
  },
  {
    id: 4,
    title: "E-commerce Store",
    description: "Modern online store with shopping cart and checkout",
    category: "E-commerce",
    techStack: "React + Tailwind",
    features: ["Product catalog", "Shopping cart", "User accounts", "Payment integration"],
    icon: ShoppingCart,
    color: "green",
    image: "/api/placeholder/400/300"
  },
  {
    id: 5,
    title: "Agency Website",
    description: "Professional agency site showcasing services and team",
    category: "Agency",
    techStack: "HTML/CSS/JS",
    features: ["Service pages", "Team profiles", "Case studies", "Contact forms"],
    icon: Palette,
    color: "indigo",
    image: "/api/placeholder/400/300"
  },
  {
    id: 6,
    title: "Non-Profit Website",
    description: "Inspiring website for charitable organizations",
    category: "Non-Profit",
    techStack: "React + Tailwind",
    features: ["Donation system", "Event listings", "Volunteer signup", "Impact stories"],
    icon: Heart,
    color: "red",
    image: "/api/placeholder/400/300"
  }
]

const categories = ["All", "Portfolio", "Business", "SaaS", "E-commerce", "Agency", "Non-Profit"]

export default function ExamplesPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-stone-50 relative">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-slate-200/30 to-stone-200/30 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-stone-200/30 to-slate-200/30 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 py-20 relative">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-white border border-slate-200 mb-8 shadow-sm">
            <Palette className="h-4 w-4 text-slate-600 mr-2" />
            <span className="text-sm font-medium text-slate-700">AI-Generated Examples</span>
          </div>
          <h1 className="text-5xl md:text-7xl font-bold text-slate-900 mb-6 leading-tight">
            Example 
            <span className="block text-slate-700">
              Websites
            </span>
          </h1>
          <p className="text-xl md:text-2xl text-slate-600 max-w-4xl mx-auto leading-relaxed">
            Explore websites created with AI SiteForge. Get inspired and see what's possible 
            with our AI-powered generation technology.
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-3 mb-16">
          {categories.map((category) => (
            <Button
              key={category}
              variant={category === "All" ? "default" : "outline"}
              size="sm"
              className={`mb-2 transition-all duration-300 ${
                category === "All" 
                  ? "" 
                  : "bg-white hover:bg-slate-50 shadow-sm hover:shadow-md"
              }`}
            >
              {category}
            </Button>
          ))}
        </div>

        {/* Examples Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {examples.map((example) => {
            const Icon = example.icon
            
            return (
              <Card key={example.id} className="border-slate-200 shadow-sm hover:shadow-md transition-all duration-300 group">
                <CardHeader className="pb-4">
                  <div className="aspect-video bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl mb-4 flex items-center justify-center overflow-hidden">
                    <div className="w-20 h-20 bg-slate-100 rounded-xl flex items-center justify-center">
                      <Icon className="h-10 w-10 text-slate-600" />
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{example.title}</CardTitle>
                    <span className="px-2 py-1 text-xs font-medium bg-slate-100 text-slate-700 rounded-full">
                      {example.category}
                    </span>
                  </div>
                  <CardDescription className="mt-2">
                    {example.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Tech Stack:</span>
                      <span className="font-medium">{example.techStack}</span>
                    </div>
                    
                    <div>
                      <h4 className="text-sm font-medium text-gray-700 mb-2">Features:</h4>
                      <div className="grid grid-cols-2 gap-1">
                        {example.features.map((feature, index) => (
                          <div key={index} className="text-xs text-gray-600 flex items-center">
                            <div className="w-1.5 h-1.5 bg-slate-500 rounded-full mr-2"></div>
                            {feature}
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="flex gap-2 pt-2">
                      <Button variant="outline" size="sm" className="flex-1">
                        <Eye className="mr-1 h-3 w-3" />
                        Preview
                      </Button>
                      <Button size="sm" className="flex-1">
                        <ArrowRight className="mr-1 h-3 w-3" />
                        Use Template
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* CTA Section */}
        <Card className="border-slate-200 shadow-sm bg-slate-900 text-white text-center">
          <CardContent className="py-12">
            <h2 className="text-3xl font-bold mb-4">
              Ready to Create Your Own?
            </h2>
            <p className="text-xl opacity-90 mb-8 max-w-2xl mx-auto">
              Use any of these examples as inspiration or describe your own unique vision. 
              Our AI will bring it to life in minutes.
            </p>
            <Link href="/create">
              <Button size="lg" variant="secondary" className="text-lg px-8 py-6">
                Start Creating <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
