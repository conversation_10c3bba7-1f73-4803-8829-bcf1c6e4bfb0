"use client"

import { useState, useEffect } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"

import { Upload, <PERSON>rkles, ArrowRight, Image as ImageIcon, X, Link, Plus, Trash2, Lightbulb, Zap, Eye, HelpCircle, CheckCircle, Wand2, Palette, Layout, Globe, Play } from "lucide-react"
import Image from "next/image"
import { Onboarding } from "@/components/ui/onboarding"

export default function CreatePage() {
  const router = useRouter()
  const [prompt, setPrompt] = useState("")
  const [screenshot, setScreenshot] = useState<File | null>(null)
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [referenceUrls, setReferenceUrls] = useState<string[]>([])
  const [newUrl, setNewUrl] = useState("")
  const [error, setError] = useState<string | null>(null)
  const [currentStep, setCurrentStep] = useState(1)
  const [showTips, setShowTips] = useState(true)
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)
  const [isTyping, setIsTyping] = useState(false)
  const [showOnboarding, setShowOnboarding] = useState(false)

  // Auto-save to localStorage and check for first-time users
  useEffect(() => {
    const savedPrompt = localStorage.getItem('siteforge-prompt')
    if (savedPrompt && !prompt) {
      setPrompt(savedPrompt)
    }
    
    // Check if this is a first-time user
    const hasSeenOnboarding = localStorage.getItem('siteforge-onboarding-shown')
    if (!hasSeenOnboarding) {
      setShowOnboarding(true)
    }
  }, [])

  useEffect(() => {
    if (prompt) {
      localStorage.setItem('siteforge-prompt', prompt)
    }
  }, [prompt])

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    try {
      const file = event.target.files?.[0]
      if (file && file.type.startsWith('image/')) {
        if (file.size > 10 * 1024 * 1024) {
          setError('File size must be less than 10MB')
          return
        }
        setScreenshot(file)
        const url = URL.createObjectURL(file)
        setPreviewUrl(url)
        setError(null)
      } else {
        setError('Please upload a valid image file')
      }
    } catch (error) {
      console.error('Error handling file upload:', error)
      setError('Failed to upload image. Please try again.')
    }
  }

  const removeScreenshot = () => {
    try {
      setScreenshot(null)
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl)
        setPreviewUrl(null)
      }
    } catch (error) {
      console.error('Error removing screenshot:', error)
    }
  }

  const isValidUrl = (string: string) => {
    try {
      new URL(string)
      return true
    } catch (_) {
      return false
    }
  }

  const addReferenceUrl = () => {
    try {
      if (newUrl.trim() && isValidUrl(newUrl.trim()) && !referenceUrls.includes(newUrl.trim())) {
        setReferenceUrls([...referenceUrls, newUrl.trim()])
        setNewUrl("")
      }
    } catch (error) {
      console.error('Error adding reference URL:', error)
    }
  }

  const removeReferenceUrl = (index: number) => {
    try {
      setReferenceUrls(referenceUrls.filter((_, i) => i !== index))
    } catch (error) {
      console.error('Error removing reference URL:', error)
    }
  }

  const websiteCategories = [
    { id: 'portfolio', name: 'Portfolio', icon: Palette, description: 'Showcase your work and skills' },
    { id: 'business', name: 'Business', icon: Globe, description: 'Professional company websites' },
    { id: 'ecommerce', name: 'E-commerce', icon: Zap, description: 'Online stores and shops' },
    { id: 'blog', name: 'Blog', icon: Layout, description: 'Content and publishing sites' },
    { id: 'landing', name: 'Landing Page', icon: Eye, description: 'Marketing and conversion pages' },
    { id: 'other', name: 'Other', icon: Wand2, description: 'Custom or unique websites' }
  ]

  const examplePrompts = {
    portfolio: [
      "A clean, minimalist portfolio for a UX designer with a hero section, project gallery with hover effects, about section, and contact form. Use white background with blue accents.",
      "A creative photographer portfolio with full-screen image galleries, dark theme, smooth scrolling, and client testimonials.",
      "A modern developer portfolio showcasing coding projects, skills section with progress bars, and a blog section."
    ],
    business: [
      "A professional law firm website with services overview, attorney profiles, case studies, and contact information. Use navy blue and gold colors.",
      "A modern consulting agency site with service offerings, team bios, client testimonials, and a contact form.",
      "A medical practice website with appointment booking, services, doctor profiles, and patient resources."
    ],
    ecommerce: [
      "An online clothing store with product grid, filtering options, shopping cart, product detail pages with image gallery, and checkout process.",
      "A tech gadgets e-commerce site with product comparisons, reviews, wishlist functionality, and secure checkout.",
      "A handmade crafts marketplace with seller profiles, product categories, and integrated payment system."
    ],
    blog: [
      "A travel blog with featured articles, category filters, author bio, newsletter signup, and social media integration.",
      "A food blog with recipe cards, ingredient lists, cooking tips, and photo galleries.",
      "A tech blog with article categories, search functionality, author profiles, and comment system."
    ],
    landing: [
      "A modern SaaS landing page with gradient background, feature highlights with icons, pricing tiers, customer testimonials, and a prominent sign-up call-to-action.",
      "A fitness app landing page with hero video, feature showcase, pricing plans, and download buttons.",
      "A course landing page with instructor bio, curriculum overview, student testimonials, and enrollment form."
    ],
    other: [
      "A warm, inviting restaurant website with a large hero image of food, menu section with categories, location and hours, online reservation form, and customer testimonials.",
      "A non-profit organization website with mission statement, donation functionality, volunteer opportunities, and impact stories.",
      "An event planning website with service packages, portfolio gallery, testimonials, and booking form."
    ]
  }

  const handleOnboardingComplete = () => {
    setShowOnboarding(false)
    localStorage.setItem('siteforge-onboarding-shown', 'true')
  }

  const handleShowOnboarding = () => {
    setShowOnboarding(true)
  }

  const handleSubmit = async () => {
    try {
      if (!prompt.trim()) return

      setIsLoading(true)
      localStorage.removeItem('siteforge-prompt') // Clear saved prompt on submit

      // Simulate processing time
      setTimeout(() => {
        try {
          // Navigate to tech stack selection with the prompt and screenshot data
          const params = new URLSearchParams()
          params.set('prompt', prompt)
          if (screenshot) {
            params.set('hasScreenshot', 'true')
          }
          if (referenceUrls.length > 0) {
            params.set('referenceUrls', JSON.stringify(referenceUrls))
          }
          if (selectedCategory) {
            params.set('category', selectedCategory)
          }
          router.push(`/create/tech-stack?${params.toString()}`)
        } catch (error) {
          console.error('Error during navigation:', error)
          setIsLoading(false)
        }
      }, 1000)
    } catch (error) {
      console.error('Error in handleSubmit:', error)
      setIsLoading(false)
    }
  }

  // Global error handler
  const handleGlobalError = (error: Error, errorInfo: any) => {
    console.error('Global error in CreatePage:', error, errorInfo)
    setError('An unexpected error occurred. Please try again.')
  }

  // Wrap the entire component in error boundary
  try {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-stone-50 relative">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-slate-200/30 to-stone-200/30 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-stone-200/30 to-slate-200/30 rounded-full blur-3xl animate-pulse" style={{animationDelay: '2s'}}></div>
        <div className="absolute top-1/3 left-1/4 w-2 h-2 bg-slate-300 rounded-full animate-ping opacity-75"></div>
        <div className="absolute bottom-1/3 right-1/4 w-1 h-1 bg-slate-400 rounded-full animate-ping opacity-75" style={{animationDelay: '1s'}}></div>
      </div>

      <div className="container mx-auto px-4 py-20 relative">
        {/* Progress Indicator */}
        <div className="max-w-md mx-auto mb-12">
          <div className="flex items-center justify-between text-xs text-slate-500 mb-2">
            <span>Step 1 of 3</span>
            <span>Describe Website</span>
          </div>
          <div className="w-full bg-slate-200 rounded-full h-1">
            <div className="bg-slate-600 h-1 rounded-full w-1/3"></div>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="max-w-4xl mx-auto mb-6">
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-5 h-5 bg-red-400 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs font-bold">!</span>
                  </div>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-red-800">{error}</p>
                </div>
                <div className="ml-auto pl-3">
                  <button
                    onClick={() => setError(null)}
                    className="text-red-400 hover:text-red-600"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Header */}
        <div className="text-center mb-16">
          <div className="flex items-center justify-center gap-4 mb-8">
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-white border border-slate-200 shadow-sm">
              <Sparkles className="h-4 w-4 text-slate-600 mr-2" />
              <span className="text-sm font-medium text-slate-700">AI Website Generator</span>
              <span className="ml-2 px-2 py-1 text-xs bg-slate-100 text-slate-600 rounded-full">Beta</span>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleShowOnboarding}
              className="flex items-center gap-2 text-slate-600 hover:text-slate-700"
            >
              <Play className="h-4 w-4" />
              Tutorial
            </Button>
          </div>
          <h1 className="text-5xl md:text-7xl font-bold text-slate-900 mb-6 leading-tight">
            Create Your
            <span className="block text-slate-700">
              Perfect Website
            </span>
          </h1>
          <p className="text-xl md:text-2xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
            Describe your vision and let AI bring it to life in seconds
          </p>
          
          {/* Quick Stats */}
          <div className="flex flex-wrap justify-center gap-6 mt-8 text-sm text-slate-600">
            <div className="flex items-center">
              <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
              <span>Under 20s generation</span>
            </div>
            <div className="flex items-center">
              <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
              <span>Mobile responsive</span>
            </div>
            <div className="flex items-center">
              <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
              <span>SEO optimized</span>
            </div>
          </div>
        </div>

        {/* Main Content - Simplified Interface */}
        <div className="max-w-4xl mx-auto">
          {/* Quick Template Selection */}
          {!selectedCategory && (
            <Card className="border-slate-200 shadow-sm mb-8">
              <CardHeader className="text-center pb-6">
                <CardTitle className="text-2xl text-slate-900 mb-2">
                  What type of website do you want to create?
                </CardTitle>
                <CardDescription className="text-lg text-slate-600">
                  Choose a category to get started with tailored examples and guidance
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {websiteCategories.map((category) => {
                    const Icon = category.icon
                    return (
                      <button
                        key={category.id}
                        onClick={() => setSelectedCategory(category.id)}
                        className="p-4 rounded-lg border border-slate-200 hover:border-slate-300 hover:bg-slate-50 text-left transition-all group"
                      >
                        <Icon className="h-6 w-6 text-slate-600 mb-3 group-hover:text-slate-700" />
                        <div className="font-semibold text-slate-900 mb-1">{category.name}</div>
                        <div className="text-sm text-slate-600">{category.description}</div>
                      </button>
                    )
                  })}
                </div>
                <div className="text-center mt-6">
                  <Button 
                    variant="ghost" 
                    onClick={() => setSelectedCategory('custom')}
                    className="text-slate-600 hover:text-slate-700"
                  >
                    Skip and describe from scratch
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Main Description Card */}
          {(selectedCategory || selectedCategory === 'custom') && (
              <Card className="border-slate-200 shadow-sm mb-8">
                <CardHeader className="text-center pb-6">
                  <CardTitle className="text-2xl text-slate-900 mb-2">
                    Describe Your Website
                  </CardTitle>
                  <CardDescription className="text-lg text-slate-600">
                    Tell us what kind of website you want to create. Be as detailed as possible for the best results.
                  </CardDescription>
                  
                  {/* Tips Toggle */}
                  {showTips && (
                    <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                      <div className="flex items-start gap-3">
                        <Lightbulb className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                        <div className="text-left">
                          <h4 className="font-medium text-blue-900 mb-2">💡 Tips for better results:</h4>
                          <ul className="text-sm text-blue-800 space-y-1">
                            <li>• Mention your industry or purpose (e.g., "restaurant", "portfolio")</li>
                            <li>• Describe the style you want (e.g., "modern", "minimalist", "colorful")</li>
                            <li>• List specific sections needed (e.g., "contact form", "gallery")</li>
                            <li>• Include color preferences if you have any</li>
                          </ul>
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            onClick={() => setShowTips(false)}
                            className="mt-2 text-blue-600 hover:text-blue-700"
                          >
                            Got it, hide tips
                          </Button>
                        </div>
                      </div>
                    </div>
                  )}
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {/* Category Selection */}
                    <div>
                      <label className="block text-sm font-medium text-slate-700 mb-3">
                        Website Category (Optional)
                      </label>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                        {websiteCategories.map((category) => {
                          const Icon = category.icon
                          return (
                            <button
                              key={category.id}
                              onClick={() => setSelectedCategory(category.id === selectedCategory ? null : category.id)}
                              className={`p-3 rounded-lg border text-left transition-all ${
                                selectedCategory === category.id
                                  ? 'border-slate-600 bg-slate-50 shadow-sm'
                                  : 'border-slate-200 hover:border-slate-300 hover:bg-slate-50'
                              }`}
                            >
                              <Icon className="h-5 w-5 text-slate-600 mb-2" />
                              <div className="font-medium text-slate-900 text-sm">{category.name}</div>
                              <div className="text-xs text-slate-600">{category.description}</div>
                            </button>
                          )
                        })}
                      </div>
                    </div>

                    <div>
                      <label htmlFor="prompt" className="block text-sm font-medium text-slate-700 mb-3">
                        Website Description *
                      </label>
                      <Textarea
                        id="prompt"
                        placeholder={selectedCategory && examplePrompts[selectedCategory as keyof typeof examplePrompts] 
                          ? `e.g., ${examplePrompts[selectedCategory as keyof typeof examplePrompts][0]}`
                          : "e.g., A modern portfolio website for a graphic designer with a dark theme, image gallery, contact form, and smooth animations..."}
                        value={prompt}
                        onChange={(e) => {
                          setPrompt(e.target.value)
                          setIsTyping(true)
                          setTimeout(() => setIsTyping(false), 1000)
                        }}
                        className="min-h-[150px] text-base focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-all duration-200"
                        maxLength={1000}
                      />
                      <div className="flex items-center justify-between mt-3">
                        <p className="text-sm text-slate-500">
                          {prompt.length}/1000 characters
                        </p>
                        <div className="flex items-center space-x-2">
                          {isTyping && (
                            <div className="flex items-center space-x-1">
                              <div className="w-1 h-1 bg-slate-400 rounded-full animate-pulse"></div>
                              <div className="w-1 h-1 bg-slate-400 rounded-full animate-pulse" style={{animationDelay: '0.2s'}}></div>
                              <div className="w-1 h-1 bg-slate-400 rounded-full animate-pulse" style={{animationDelay: '0.4s'}}></div>
                            </div>
                          )}
                          {prompt.length > 0 && !isTyping && (
                            <div className={`w-2 h-2 rounded-full ${
                              prompt.length < 50 ? 'bg-red-400' :
                              prompt.length < 100 ? 'bg-yellow-400' : 'bg-green-400'
                            }`}></div>
                          )}
                          <span className="text-xs text-slate-500">
                            {prompt.length < 50 ? 'Add more details for better results' :
                             prompt.length < 100 ? 'Good start, add more details' : 'Great description!'}
                          </span>
                        </div>
                      </div>
                      
                      {/* Quick Examples for Selected Category */}
                      {selectedCategory && examplePrompts[selectedCategory as keyof typeof examplePrompts] && (
                        <div className="mt-4">
                          <p className="text-sm font-medium text-slate-700 mb-2">Quick examples for {websiteCategories.find(c => c.id === selectedCategory)?.name}:</p>
                          <div className="space-y-2">
                            {examplePrompts[selectedCategory as keyof typeof examplePrompts].slice(0, 2).map((example, index) => (
                              <button
                                key={index}
                                onClick={() => setPrompt(example)}
                                className="w-full text-left p-3 text-sm bg-slate-50 hover:bg-slate-100 border border-slate-200 rounded-lg transition-colors"
                              >
                                {example.substring(0, 120)}...
                              </button>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>

                {/* Reference URLs Section */}
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-3">
                    Reference Website Links (Optional)
                  </label>
                  <p className="text-sm text-slate-600 mb-4">
                    Add URLs of websites you like for design inspiration and reference
                  </p>

                  <div className="space-y-3">
                    <div className="flex gap-2">
                      <Input
                        type="url"
                        placeholder="https://example.com"
                        value={newUrl}
                        onChange={(e) => setNewUrl(e.target.value)}
                        className="flex-1"
                        onKeyPress={(e) => e.key === 'Enter' && addReferenceUrl()}
                      />
                                              <Button
                          type="button"
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            addReferenceUrl();
                          }}
                          disabled={!newUrl.trim() || !isValidUrl(newUrl.trim())}
                          variant="outline"
                          size="sm"
                          className="px-3"
                        >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>

                    {referenceUrls.length > 0 && (
                      <div className="space-y-2">
                        <p className="text-sm font-medium text-slate-700">Reference URLs:</p>
                        {referenceUrls.map((url, index) => (
                          <div key={index} className="flex items-center gap-2 p-3 bg-slate-50 rounded-lg border border-slate-200">
                            <Link className="h-4 w-4 text-slate-500 flex-shrink-0" />
                            <span className="text-sm text-slate-700 flex-1 truncate">{url}</span>
                            <Button
                              type="button"
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                removeReferenceUrl(index);
                              }}
                              variant="ghost"
                              size="sm"
                              className="p-1 h-auto text-slate-500"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>

                {/* Screenshot Upload Section */}
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-3">
                    Reference Screenshot (Optional)
                  </label>
                  <div className="border-2 border-dashed border-slate-300 rounded-lg p-6 text-center">
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleFileUpload}
                      className="hidden"
                      id="screenshot-upload"
                    />
                    <label htmlFor="screenshot-upload" className="cursor-pointer">
                      <Upload className="h-8 w-8 text-slate-400 mx-auto mb-2" />
                      <p className="text-sm text-slate-600">
                        Drop an image here or click to upload
                      </p>
                      <p className="text-xs text-slate-500 mt-1">
                        PNG, JPG, GIF up to 10MB
                      </p>
                    </label>
                  </div>
                </div>

                {previewUrl && (
                  <div className="relative">
                    <div className="relative rounded-lg overflow-hidden border">
                      <Image
                        src={previewUrl}
                        alt="Screenshot preview"
                        width={400}
                        height={300}
                        className="w-full h-48 object-cover"
                      />
                      <button
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          removeScreenshot();
                        }}
                        className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                    <p className="text-sm text-slate-600 mt-2">
                      Screenshot uploaded: {screenshot?.name}
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
          )}

          {/* Action Button */}
          <div className="flex justify-center mb-16">
            <Button
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleSubmit();
              }}
              disabled={!prompt.trim() || isLoading}
              size="lg"
              className="text-lg px-12 py-6 relative overflow-hidden group"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-slate-600 to-slate-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <span className="relative flex items-center">
                {isLoading ? (
                  <>
                    <Sparkles className="mr-2 h-5 w-5 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    Choose Tech Stack
                    <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-200" />
                  </>
                )}
              </span>
            </Button>
          </div>

          {/* Help Section */}
          <Card className="border-slate-200 shadow-sm">
            <CardHeader className="text-center">
              <CardTitle className="text-xl text-slate-900 mb-2 flex items-center justify-center gap-2">
                <HelpCircle className="h-5 w-5" />
                Need Help?
              </CardTitle>
              <CardDescription className="text-slate-600">
                Get the most out of SiteForge with these tips
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <Lightbulb className="h-6 w-6 text-blue-600" />
                  </div>
                  <h4 className="font-semibold text-slate-900 mb-2">Be Specific</h4>
                  <p className="text-sm text-slate-600">
                    Include details about colors, layout, sections, and functionality you want.
                  </p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <Eye className="h-6 w-6 text-green-600" />
                  </div>
                  <h4 className="font-semibold text-slate-900 mb-2">Use References</h4>
                  <p className="text-sm text-slate-600">
                    Upload screenshots or add URLs of websites you like for inspiration.
                  </p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <Zap className="h-6 w-6 text-purple-600" />
                  </div>
                  <h4 className="font-semibold text-slate-900 mb-2">Iterate Quickly</h4>
                  <p className="text-sm text-slate-600">
                    Generate, review, and refine. Each iteration gets you closer to perfect.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
       </div>
        </div>
        
        {/* Onboarding Modal */}
        <Onboarding
          isOpen={showOnboarding}
          onClose={() => setShowOnboarding(false)}
          onComplete={handleOnboardingComplete}
        />
      </div>
    )
 } catch (error) {
   console.error('Error rendering CreatePage:', error)
   return (
     <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-stone-50 flex items-center justify-center">
       <div className="text-center">
         <h1 className="text-2xl font-bold text-slate-900 mb-4">Something went wrong</h1>
         <p className="text-slate-600 mb-6">Please refresh the page and try again.</p>
         <button
           onClick={() => window.location.reload()}
           className="px-6 py-3 bg-slate-600 text-white rounded-lg hover:bg-slate-700 transition-colors"
         >
           Refresh Page
         </button>
       </div>
     </div>
   )
 }
}
