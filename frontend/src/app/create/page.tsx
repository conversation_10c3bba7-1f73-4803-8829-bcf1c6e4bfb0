"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Upload, <PERSON>rkles, ArrowRight, Image as ImageIcon, X, Link, Plus, Trash2 } from "lucide-react"
import Image from "next/image"

export default function CreatePage() {
  const router = useRouter()
  const [prompt, setPrompt] = useState("")
  const [screenshot, setScreenshot] = useState<File | null>(null)
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [referenceUrls, setReferenceUrls] = useState<string[]>([])
  const [newUrl, setNewUrl] = useState("")

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file && file.type.startsWith('image/')) {
      setScreenshot(file)
      const url = URL.createObjectURL(file)
      setPreviewUrl(url)
    }
  }

  const removeScreenshot = () => {
    setScreenshot(null)
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl)
      setPreviewUrl(null)
    }
  }

  const isValidUrl = (string: string) => {
    try {
      new URL(string)
      return true
    } catch (_) {
      return false
    }
  }

  const addReferenceUrl = () => {
    if (newUrl.trim() && isValidUrl(newUrl.trim()) && !referenceUrls.includes(newUrl.trim())) {
      setReferenceUrls([...referenceUrls, newUrl.trim()])
      setNewUrl("")
    }
  }

  const removeReferenceUrl = (index: number) => {
    setReferenceUrls(referenceUrls.filter((_, i) => i !== index))
  }

  const handleSubmit = async () => {
    if (!prompt.trim()) return

    setIsLoading(true)

    // Simulate processing time
    setTimeout(() => {
      // Navigate to tech stack selection with the prompt and screenshot data
      const params = new URLSearchParams()
      params.set('prompt', prompt)
      if (screenshot) {
        params.set('hasScreenshot', 'true')
      }
      if (referenceUrls.length > 0) {
        params.set('referenceUrls', JSON.stringify(referenceUrls))
      }
      router.push(`/create/tech-stack?${params.toString()}`)
    }, 1000)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-stone-50 relative">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-slate-200/30 to-stone-200/30 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-stone-200/30 to-slate-200/30 rounded-full blur-3xl animate-pulse" style={{animationDelay: '2s'}}></div>
        <div className="absolute top-1/3 left-1/4 w-2 h-2 bg-slate-300 rounded-full animate-ping opacity-75"></div>
        <div className="absolute bottom-1/3 right-1/4 w-1 h-1 bg-slate-400 rounded-full animate-ping opacity-75" style={{animationDelay: '1s'}}></div>
      </div>

      <div className="container mx-auto px-4 py-20 relative">
        {/* Progress Indicator */}
        <div className="max-w-md mx-auto mb-12">
          <div className="flex items-center justify-between text-xs text-slate-500 mb-2">
            <span>Step 1 of 3</span>
            <span>Describe Website</span>
          </div>
          <div className="w-full bg-slate-200 rounded-full h-1">
            <div className="bg-slate-600 h-1 rounded-full w-1/3"></div>
          </div>
        </div>

        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-white border border-slate-200 mb-8 shadow-sm">
            <Sparkles className="h-4 w-4 text-slate-600 mr-2" />
            <span className="text-sm font-medium text-slate-700">AI Website Generator</span>
          </div>
          <h1 className="text-5xl md:text-7xl font-bold text-slate-900 mb-6 leading-tight">
            Create Your
            <span className="block text-slate-700">
              Perfect Website
            </span>
          </h1>
          <p className="text-xl md:text-2xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
            Describe your vision and let AI bring it to life in seconds
          </p>
        </div>

        {/* Main Content - Centered Description Input */}
        <div className="max-w-4xl mx-auto">
          <Card className="border-slate-200 shadow-sm mb-8">
            <CardHeader className="text-center pb-6">
              <CardTitle className="text-2xl text-slate-900 mb-2">
                Describe Your Website
              </CardTitle>
              <CardDescription className="text-lg text-slate-600">
                Tell us what kind of website you want to create. Be as detailed as possible for the best results.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <label htmlFor="prompt" className="block text-sm font-medium text-slate-700 mb-3">
                    Website Description *
                  </label>
                  <Textarea
                    id="prompt"
                    placeholder="e.g., A modern portfolio website for a graphic designer with a dark theme, image gallery, contact form, and smooth animations..."
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    className="min-h-[150px] text-base focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-all duration-200"
                    maxLength={1000}
                  />
                  <div className="flex items-center justify-between mt-3">
                    <p className="text-sm text-slate-500">
                      {prompt.length}/1000 characters
                    </p>
                    <div className="flex items-center space-x-2">
                      {prompt.length > 0 && (
                        <div className={`w-2 h-2 rounded-full ${
                          prompt.length < 50 ? 'bg-red-400' :
                          prompt.length < 100 ? 'bg-yellow-400' : 'bg-green-400'
                        }`}></div>
                      )}
                      <span className="text-xs text-slate-500">
                        {prompt.length < 50 ? 'Add more details for better results' :
                         prompt.length < 100 ? 'Good start, add more details' : 'Great description!'}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Reference URLs Section */}
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-3">
                    Reference Website Links (Optional)
                  </label>
                  <p className="text-sm text-slate-600 mb-4">
                    Add URLs of websites you like for design inspiration and reference
                  </p>

                  <div className="space-y-3">
                    <div className="flex gap-2">
                      <Input
                        type="url"
                        placeholder="https://example.com"
                        value={newUrl}
                        onChange={(e) => setNewUrl(e.target.value)}
                        className="flex-1"
                        onKeyPress={(e) => e.key === 'Enter' && addReferenceUrl()}
                      />
                      <Button
                        type="button"
                        onClick={addReferenceUrl}
                        disabled={!newUrl.trim() || !isValidUrl(newUrl.trim())}
                        variant="outline"
                        size="sm"
                        className="px-3"
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>

                    {referenceUrls.length > 0 && (
                      <div className="space-y-2">
                        <p className="text-sm font-medium text-slate-700">Reference URLs:</p>
                        {referenceUrls.map((url, index) => (
                          <div key={index} className="flex items-center gap-2 p-3 bg-slate-50 rounded-lg border border-slate-200">
                            <Link className="h-4 w-4 text-slate-500 flex-shrink-0" />
                            <span className="text-sm text-slate-700 flex-1 truncate">{url}</span>
                            <Button
                              type="button"
                              onClick={() => removeReferenceUrl(index)}
                              variant="ghost"
                              size="sm"
                              className="p-1 h-auto text-slate-500"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>

                {/* Screenshot Upload Section */}
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-3">
                    Reference Screenshot (Optional)
                  </label>
                  <div className="border-2 border-dashed border-slate-300 rounded-lg p-6 text-center">
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleFileUpload}
                      className="hidden"
                      id="screenshot-upload"
                    />
                    <label htmlFor="screenshot-upload" className="cursor-pointer">
                      <Upload className="h-8 w-8 text-slate-400 mx-auto mb-2" />
                      <p className="text-sm text-slate-600">
                        Drop an image here or click to upload
                      </p>
                      <p className="text-xs text-slate-500 mt-1">
                        PNG, JPG, GIF up to 10MB
                      </p>
                    </label>
                  </div>
                </div>

                {previewUrl && (
                  <div className="relative">
                    <div className="relative rounded-lg overflow-hidden border">
                      <Image
                        src={previewUrl}
                        alt="Screenshot preview"
                        width={400}
                        height={300}
                        className="w-full h-48 object-cover"
                      />
                      <button
                        onClick={removeScreenshot}
                        className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                    <p className="text-sm text-slate-600 mt-2">
                      Screenshot uploaded: {screenshot?.name}
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Action Button */}
          <div className="flex justify-center mb-16">
            <Button
              onClick={handleSubmit}
              disabled={!prompt.trim() || isLoading}
              size="lg"
              className="text-lg px-12 py-6 relative overflow-hidden group"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-slate-600 to-slate-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <span className="relative flex items-center">
                {isLoading ? (
                  <>
                    <Sparkles className="mr-2 h-5 w-5 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    Choose Tech Stack
                    <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-200" />
                  </>
                )}
              </span>
            </Button>
          </div>

          {/* Examples Section */}
          <Card className="border-slate-200 shadow-sm">
            <CardHeader className="text-center">
              <CardTitle className="text-xl text-slate-900 mb-2">
                Need Inspiration?
              </CardTitle>
              <CardDescription className="text-slate-600">
                Click any example below to use it as your starting point
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div 
                  className="p-4 bg-slate-50 rounded-lg border border-slate-200 hover:bg-slate-100 transition-colors cursor-pointer group" 
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    setPrompt("A clean, minimalist portfolio for a UX designer with a hero section, project gallery with hover effects, about section, and contact form. Use white background with blue accents.");
                  }}
                >
                  <h4 className="font-medium text-slate-900 mb-2 group-hover:text-slate-700">Portfolio Website</h4>
                  <p className="text-sm text-slate-700">
                    "A clean, minimalist portfolio for a UX designer with a hero section,
                    project gallery with hover effects, about section, and contact form.
                    Use white background with blue accents."
                  </p>
                  <div className="mt-3 text-xs text-slate-500 opacity-0 group-hover:opacity-100 transition-opacity">Click to use this example →</div>
                </div>

                <div 
                  className="p-4 bg-slate-50 rounded-lg border border-slate-200 hover:bg-slate-100 transition-colors cursor-pointer group" 
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    setPrompt("A warm, inviting restaurant website with a large hero image of food, menu section with categories, location and hours, online reservation form, and customer testimonials.");
                  }}
                >
                  <h4 className="font-medium text-slate-900 mb-2 group-hover:text-slate-700">Restaurant Website</h4>
                  <p className="text-sm text-slate-700">
                    "A warm, inviting restaurant website with a large hero image of food,
                    menu section with categories, location and hours, online reservation
                    form, and customer testimonials."
                  </p>
                  <div className="mt-3 text-xs text-slate-500 opacity-0 group-hover:opacity-100 transition-opacity">Click to use this example →</div>
                </div>

                <div 
                  className="p-4 bg-slate-50 rounded-lg border border-slate-200 hover:bg-slate-100 transition-colors cursor-pointer group" 
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    setPrompt("A modern SaaS landing page with gradient background, feature highlights with icons, pricing tiers, customer testimonials, and a prominent sign-up call-to-action.");
                  }}
                >
                  <h4 className="font-medium text-slate-900 mb-2 group-hover:text-slate-700">SaaS Landing Page</h4>
                  <p className="text-sm text-slate-700">
                    "A modern SaaS landing page with gradient background, feature highlights
                    with icons, pricing tiers, customer testimonials, and a prominent
                    sign-up call-to-action."
                  </p>
                  <div className="mt-3 text-xs text-slate-500 opacity-0 group-hover:opacity-100 transition-opacity">Click to use this example →</div>
                </div>

                <div 
                  className="p-4 bg-slate-50 rounded-lg border border-slate-200 hover:bg-slate-100 transition-colors cursor-pointer group" 
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    setPrompt("An online clothing store with product grid, filtering options, shopping cart, product detail pages with image gallery, and checkout process.");
                  }}
                >
                  <h4 className="font-medium text-slate-900 mb-2 group-hover:text-slate-700">E-commerce Store</h4>
                  <p className="text-sm text-slate-700">
                    "An online clothing store with product grid, filtering options,
                    shopping cart, product detail pages with image gallery, and
                    checkout process."
                  </p>
                  <div className="mt-3 text-xs text-slate-500 opacity-0 group-hover:opacity-100 transition-opacity">Click to use this example →</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )

}
