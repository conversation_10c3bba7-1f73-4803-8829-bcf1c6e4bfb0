"use client"

import { useState, useEffect, Suspense } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ArrowRight, ArrowLeft, Code, Zap, Palette, FileCode, Star } from "lucide-react"
import { TechStack } from "@/types"

const techStacks: TechStack[] = [
  {
    id: 'html',
    name: 'HTML/CSS/JavaScript',
    description: 'Pure web technologies for fast, lightweight websites',
    icon: 'html'
  },
  {
    id: 'react',
    name: 'React + Tailwind CSS',
    description: 'Modern React framework with utility-first CSS',
    icon: 'react'
  }
]

function TechStackPageContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [selectedStack, setSelectedStack] = useState<'html' | 'react' | null>(null)
  const [prompt, setPrompt] = useState("")
  const [hasScreenshot, setHasScreenshot] = useState(false)
  const [isGenerating, setIsGenerating] = useState(false)

  useEffect(() => {
    const promptParam = searchParams.get('prompt')
    const screenshotParam = searchParams.get('hasScreenshot')
    
    if (promptParam) setPrompt(promptParam)
    if (screenshotParam === 'true') setHasScreenshot(true)
  }, [searchParams])

  const handleGenerate = async () => {
    if (!selectedStack) return

    setIsGenerating(true)
    
    // Simulate AI generation time
    setTimeout(() => {
      const params = new URLSearchParams()
      params.set('prompt', prompt)
      params.set('techStack', selectedStack)
      if (hasScreenshot) params.set('hasScreenshot', 'true')
      
      router.push(`/project/preview?${params.toString()}`)
    }, 3000)
  }

  const goBack = () => {
    router.back()
  }

  const getStackFeatures = (stackId: 'html' | 'react') => {
    if (stackId === 'html') {
      return [
        { icon: Zap, text: "Lightning fast loading" },
        { icon: FileCode, text: "Simple, clean code" },
        { icon: Star, text: "Great for static sites" },
        { icon: Code, text: "No build process needed" }
      ]
    } else {
      return [
        { icon: Zap, text: "Modern component architecture" },
        { icon: Palette, text: "Utility-first CSS framework" },
        { icon: Star, text: "Perfect for dynamic sites" },
        { icon: Code, text: "Reusable components" }
      ]
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-stone-50 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-slate-200/30 to-stone-200/30 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-stone-200/30 to-slate-200/30 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 py-12 relative">
        <div className="max-w-5xl mx-auto">
          {/* Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-white border border-slate-200 mb-8 shadow-sm">
              <Code className="h-4 w-4 text-slate-600 mr-2" />
              <span className="text-sm font-medium text-slate-700">Technology Selection</span>
            </div>
            <h1 className="text-5xl md:text-6xl font-bold text-slate-900 mb-6 leading-tight">
              Choose Your 
              <span className="block text-slate-700">
                Tech Stack
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
              Select the technology that best fits your project needs
            </p>
          </div>

          {/* Prompt Preview */}
          <Card className="mb-12 border-slate-200 shadow-sm">
            <CardHeader>
              <CardTitle className="text-xl flex items-center">
                <div className="w-8 h-8 bg-slate-100 rounded-lg flex items-center justify-center mr-3">
                  <Star className="h-4 w-4 text-slate-600" />
                </div>
                Your Project
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-slate-700 bg-slate-50 p-6 rounded-xl border border-slate-200 text-lg leading-relaxed">
                "{prompt}"
              </p>
              {hasScreenshot && (
                <div className="mt-4 p-3 bg-slate-50 border border-slate-200 rounded-lg">
                  <p className="text-sm text-slate-700 flex items-center">
                    <Star className="h-4 w-4 mr-2" />
                    Screenshot reference uploaded for enhanced accuracy
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Tech Stack Options */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            {techStacks.map((stack) => {
              const features = getStackFeatures(stack.id)
              const isSelected = selectedStack === stack.id
              
              return (
                <Card
                  key={stack.id}
                  className={`cursor-pointer transition-all duration-300 border-2 ${
                    isSelected
                      ? 'border-slate-500 bg-slate-50 shadow-lg'
                      : 'border-slate-200 hover:border-slate-300 hover:shadow-md'
                  }`}
                  onClick={() => setSelectedStack(stack.id)}
                >
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-12 h-12 bg-slate-100 rounded-lg flex items-center justify-center mr-4">
                          <Code className="h-6 w-6 text-slate-600" />
                        </div>
                        <div>
                          <CardTitle className="text-xl">{stack.name}</CardTitle>
                          <CardDescription className="mt-1">
                            {stack.description}
                          </CardDescription>
                        </div>
                      </div>
                      <div className={`w-6 h-6 rounded-full border-2 ${
                        isSelected ? 'border-slate-500 bg-slate-500' : 'border-slate-300'
                      }`}>
                        {isSelected && (
                                                      <div className="w-full h-full rounded-full bg-slate-500 flex items-center justify-center">
                            <div className="w-2 h-2 bg-white rounded-full"></div>
                          </div>
                        )}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {features.map((feature, index) => (
                        <div key={index} className="flex items-center text-sm text-gray-600">
                          <feature.icon className="h-4 w-4 mr-2 text-slate-500" />
                          {feature.text}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between">
            <Button
              variant="outline"
              onClick={goBack}
              disabled={isGenerating}
              className="flex items-center"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>

            <Button
              onClick={handleGenerate}
              disabled={!selectedStack || isGenerating}
              size="lg"
              className="text-lg px-8 py-6"
            >
              {isGenerating ? (
                <>
                  <Code className="mr-2 h-5 w-5 animate-pulse" />
                  Generating Website...
                </>
              ) : (
                <>
                  Generate Website
                  <ArrowRight className="ml-2 h-5 w-5" />
                </>
              )}
            </Button>
          </div>

          {/* Generation Progress */}
          {isGenerating && (
            <Card className="mt-8 border-0 shadow-lg">
              <CardContent className="pt-6">
                <div className="text-center">
                  <div className="mb-4">
                    <Code className="h-8 w-8 text-blue-600 mx-auto animate-pulse" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    AI is creating your website...
                  </h3>
                  <p className="text-gray-600 mb-4">
                    This usually takes 10-20 seconds
                  </p>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-blue-600 h-2 rounded-full animate-pulse" style={{width: '60%'}}></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}

export default function TechStackPage() {
  return (
    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
      <TechStackPageContent />
    </Suspense>
  )
}
