"use client"

import { useState, useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { HelpCircle, X, Lightbulb, CheckCircle, ArrowRight, Info, Zap, Target, Eye } from "lucide-react"

interface TooltipProps {
  content: string
  children: React.ReactNode
  position?: 'top' | 'bottom' | 'left' | 'right'
  trigger?: 'hover' | 'click'
}

interface HelpStep {
  id: string
  title: string
  content: string
  element?: string
  position?: 'top' | 'bottom' | 'left' | 'right'
  icon?: React.ComponentType<any>
}

interface ContextualHelpProps {
  steps: HelpStep[]
  isActive: boolean
  onComplete: () => void
  onClose: () => void
}

export function Tooltip({ content, children, position = 'top', trigger = 'hover' }: TooltipProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [actualPosition, setActualPosition] = useState(position)
  const tooltipRef = useRef<HTMLDivElement>(null)
  const triggerRef = useRef<HTMLDivElement>(null)

  const showTooltip = () => setIsVisible(true)
  const hideTooltip = () => setIsVisible(false)

  useEffect(() => {
    if (isVisible && tooltipRef.current && triggerRef.current) {
      const tooltip = tooltipRef.current
      const trigger = triggerRef.current
      const rect = trigger.getBoundingClientRect()
      const tooltipRect = tooltip.getBoundingClientRect()
      
      // Check if tooltip would go off screen and adjust position
      let newPosition = position
      
      if (position === 'top' && rect.top - tooltipRect.height < 10) {
        newPosition = 'bottom'
      } else if (position === 'bottom' && rect.bottom + tooltipRect.height > window.innerHeight - 10) {
        newPosition = 'top'
      } else if (position === 'left' && rect.left - tooltipRect.width < 10) {
        newPosition = 'right'
      } else if (position === 'right' && rect.right + tooltipRect.width > window.innerWidth - 10) {
        newPosition = 'left'
      }
      
      setActualPosition(newPosition)
    }
  }, [isVisible, position])

  const positionClasses = {
    top: 'bottom-full left-1/2 transform -translate-x-1/2 mb-2',
    bottom: 'top-full left-1/2 transform -translate-x-1/2 mt-2',
    left: 'right-full top-1/2 transform -translate-y-1/2 mr-2',
    right: 'left-full top-1/2 transform -translate-y-1/2 ml-2'
  }

  const arrowClasses = {
    top: 'top-full left-1/2 transform -translate-x-1/2 border-l-transparent border-r-transparent border-b-transparent border-t-slate-800',
    bottom: 'bottom-full left-1/2 transform -translate-x-1/2 border-l-transparent border-r-transparent border-t-transparent border-b-slate-800',
    left: 'left-full top-1/2 transform -translate-y-1/2 border-t-transparent border-b-transparent border-r-transparent border-l-slate-800',
    right: 'right-full top-1/2 transform -translate-y-1/2 border-t-transparent border-b-transparent border-l-transparent border-r-slate-800'
  }

  return (
    <div 
      ref={triggerRef}
      className="relative inline-block"
      onMouseEnter={trigger === 'hover' ? showTooltip : undefined}
      onMouseLeave={trigger === 'hover' ? hideTooltip : undefined}
      onClick={trigger === 'click' ? () => setIsVisible(!isVisible) : undefined}
    >
      {children}
      
      {isVisible && (
        <div
          ref={tooltipRef}
          className={`absolute z-50 px-3 py-2 text-sm text-white bg-slate-800 rounded-lg shadow-lg whitespace-nowrap max-w-xs ${positionClasses[actualPosition]}`}
        >
          {content}
          <div className={`absolute w-0 h-0 border-4 ${arrowClasses[actualPosition]}`} />
        </div>
      )}
    </div>
  )
}

export function ContextualHelp({ steps, isActive, onComplete, onClose }: ContextualHelpProps) {
  const [currentStep, setCurrentStep] = useState(0)
  const [highlightedElement, setHighlightedElement] = useState<HTMLElement | null>(null)

  useEffect(() => {
    if (isActive && steps.length > 0) {
      const step = steps[currentStep]
      if (step.element) {
        const element = document.querySelector(step.element) as HTMLElement
        if (element) {
          setHighlightedElement(element)
          element.scrollIntoView({ behavior: 'smooth', block: 'center' })
          
          // Add highlight class
          element.classList.add('contextual-help-highlight')
        }
      }
    }

    return () => {
      if (highlightedElement) {
        highlightedElement.classList.remove('contextual-help-highlight')
      }
    }
  }, [currentStep, isActive, steps])

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      if (highlightedElement) {
        highlightedElement.classList.remove('contextual-help-highlight')
      }
      setCurrentStep(currentStep + 1)
    } else {
      onComplete()
    }
  }

  const handlePrevious = () => {
    if (currentStep > 0) {
      if (highlightedElement) {
        highlightedElement.classList.remove('contextual-help-highlight')
      }
      setCurrentStep(currentStep - 1)
    }
  }

  const handleClose = () => {
    if (highlightedElement) {
      highlightedElement.classList.remove('contextual-help-highlight')
    }
    onClose()
  }

  if (!isActive || steps.length === 0) return null

  const currentStepData = steps[currentStep]
  const Icon = currentStepData.icon || Info

  return (
    <>
      {/* Overlay */}
      <div className="fixed inset-0 bg-black/30 backdrop-blur-sm z-40" />
      
      {/* Help Card */}
      <div className="fixed top-4 right-4 z-50 w-80">
        <Card className="shadow-xl border-2 border-blue-200">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Icon className="h-5 w-5 text-blue-600" />
                <CardTitle className="text-lg">{currentStepData.title}</CardTitle>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClose}
                className="text-slate-500 hover:text-slate-700"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            
            {/* Progress indicator */}
            <div className="flex items-center space-x-1 mt-2">
              {steps.map((_, index) => (
                <div
                  key={index}
                  className={`h-1.5 flex-1 rounded-full ${
                    index <= currentStep ? 'bg-blue-600' : 'bg-slate-200'
                  }`}
                />
              ))}
            </div>
          </CardHeader>
          
          <CardContent>
            <p className="text-slate-700 mb-4">{currentStepData.content}</p>
            
            <div className="flex items-center justify-between">
              <Button
                variant="outline"
                size="sm"
                onClick={handlePrevious}
                disabled={currentStep === 0}
              >
                Previous
              </Button>
              
              <span className="text-sm text-slate-500">
                {currentStep + 1} of {steps.length}
              </span>
              
              <Button
                size="sm"
                onClick={handleNext}
                className="flex items-center gap-2"
              >
                {currentStep === steps.length - 1 ? (
                  <>
                    <CheckCircle className="h-4 w-4" />
                    Finish
                  </>
                ) : (
                  <>
                    Next
                    <ArrowRight className="h-4 w-4" />
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  )
}

// Predefined help flows for different parts of the application
export const helpFlows = {
  createWebsite: [
    {
      id: 'welcome',
      title: 'Welcome to SiteForge',
      content: 'Let\'s walk through creating your first website. This guided tour will show you all the key features.',
      icon: Zap
    },
    {
      id: 'category',
      title: 'Choose Website Type',
      content: 'Start by selecting a category that matches your website type. This helps us provide better examples and suggestions.',
      element: '[data-help="category-selection"]',
      icon: Target
    },
    {
      id: 'description',
      title: 'Describe Your Website',
      content: 'Write a detailed description of what you want. Include colors, layout, sections, and functionality for best results.',
      element: '[data-help="website-description"]',
      icon: Lightbulb
    },
    {
      id: 'references',
      title: 'Add References (Optional)',
      content: 'Upload screenshots or add URLs of websites you like. This helps our AI understand your preferred style.',
      element: '[data-help="reference-section"]',
      icon: Eye
    },
    {
      id: 'generate',
      title: 'Generate Your Website',
      content: 'Click "Choose Tech Stack" to continue to the next step where you can select your preferred technology stack.',
      element: '[data-help="generate-button"]',
      icon: CheckCircle
    }
  ],
  
  sectionBuilder: [
    {
      id: 'overview',
      title: 'Section Builder',
      content: 'Use this visual editor to arrange and customize your website sections. You can drag, drop, edit, and reorder sections.',
      icon: Zap
    },
    {
      id: 'add-section',
      title: 'Add New Sections',
      content: 'Click "Add Section" to choose from various section types like Hero, About, Services, Gallery, and more.',
      element: '[data-help="add-section"]',
      icon: Target
    },
    {
      id: 'drag-drop',
      title: 'Reorder Sections',
      content: 'Drag sections by the grip handle to reorder them. Your website will be built in this exact order.',
      element: '[data-help="section-list"]',
      icon: Lightbulb
    },
    {
      id: 'edit-content',
      title: 'Edit Section Content',
      content: 'Click on any section title or content to edit it inline. Describe what you want in each section.',
      element: '[data-help="section-content"]',
      icon: Eye
    }
  ]
}

// Hook for managing contextual help state
export function useContextualHelp() {
  const [activeFlow, setActiveFlow] = useState<string | null>(null)
  const [isHelpActive, setIsHelpActive] = useState(false)

  const startHelp = (flowName: keyof typeof helpFlows) => {
    setActiveFlow(flowName)
    setIsHelpActive(true)
  }

  const stopHelp = () => {
    setActiveFlow(null)
    setIsHelpActive(false)
  }

  const completeHelp = () => {
    if (activeFlow) {
      localStorage.setItem(`siteforge-help-${activeFlow}`, 'completed')
    }
    stopHelp()
  }

  const hasSeenHelp = (flowName: keyof typeof helpFlows) => {
    return localStorage.getItem(`siteforge-help-${flowName}`) === 'completed'
  }

  return {
    activeFlow,
    isHelpActive,
    startHelp,
    stopHelp,
    completeHelp,
    hasSeenHelp,
    currentSteps: activeFlow ? helpFlows[activeFlow] : []
  }
}
