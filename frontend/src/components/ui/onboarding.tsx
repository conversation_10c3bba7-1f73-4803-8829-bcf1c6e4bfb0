"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { X, ArrowRight, ArrowLeft, Sparkles, CheckCircle, Play, Lightbulb, Target, Zap } from "lucide-react"

interface OnboardingStep {
  id: string
  title: string
  description: string
  content: React.ReactNode
  icon: React.ComponentType<any>
}

interface OnboardingProps {
  isOpen: boolean
  onClose: () => void
  onComplete: () => void
}

export function Onboarding({ isOpen, onClose, onComplete }: OnboardingProps) {
  const [currentStep, setCurrentStep] = useState(0)
  const [completedSteps, setCompletedSteps] = useState<string[]>([])

  const steps: OnboardingStep[] = [
    {
      id: "welcome",
      title: "Welcome to SiteForge! 🎉",
      description: "Let's get you started with creating amazing websites in minutes",
      icon: Sparkles,
      content: (
        <div className="space-y-4">
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <Sparkles className="h-8 w-8 text-white" />
            </div>
            <h3 className="text-xl font-semibold text-slate-900 mb-2">You're in the right place!</h3>
            <p className="text-slate-600 mb-6">
              SiteForge uses AI to transform your ideas into beautiful, functional websites. 
              No coding required - just describe what you want!
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
            <div className="p-4 bg-blue-50 rounded-lg">
              <Target className="h-6 w-6 text-blue-600 mx-auto mb-2" />
              <h4 className="font-medium text-slate-900 mb-1">Describe Your Vision</h4>
              <p className="text-sm text-slate-600">Tell us what kind of website you want</p>
            </div>
            <div className="p-4 bg-green-50 rounded-lg">
              <Zap className="h-6 w-6 text-green-600 mx-auto mb-2" />
              <h4 className="font-medium text-slate-900 mb-1">AI Generates Code</h4>
              <p className="text-sm text-slate-600">Our AI creates your website in seconds</p>
            </div>
            <div className="p-4 bg-purple-50 rounded-lg">
              <CheckCircle className="h-6 w-6 text-purple-600 mx-auto mb-2" />
              <h4 className="font-medium text-slate-900 mb-1">Launch & Share</h4>
              <p className="text-sm text-slate-600">Your website is ready to go live</p>
            </div>
          </div>
        </div>
      )
    },
    {
      id: "categories",
      title: "Choose Your Website Type",
      description: "Start with a category to get tailored examples and guidance",
      icon: Target,
      content: (
        <div className="space-y-4">
          <div className="text-center mb-6">
            <Target className="h-12 w-12 text-slate-600 mx-auto mb-3" />
            <h3 className="text-lg font-semibold text-slate-900 mb-2">Pick a category that fits your needs</h3>
            <p className="text-slate-600">
              This helps us provide better examples and suggestions for your specific type of website.
            </p>
          </div>
          
          <div className="grid grid-cols-2 gap-3">
            {[
              { name: "Portfolio", desc: "Showcase your work", color: "bg-blue-50 text-blue-700" },
              { name: "Business", desc: "Professional sites", color: "bg-green-50 text-green-700" },
              { name: "E-commerce", desc: "Online stores", color: "bg-purple-50 text-purple-700" },
              { name: "Blog", desc: "Content sites", color: "bg-orange-50 text-orange-700" },
              { name: "Landing Page", desc: "Marketing pages", color: "bg-pink-50 text-pink-700" },
              { name: "Other", desc: "Custom projects", color: "bg-slate-50 text-slate-700" }
            ].map((category) => (
              <div key={category.name} className={`p-3 rounded-lg border border-slate-200 ${category.color}`}>
                <div className="font-medium text-sm">{category.name}</div>
                <div className="text-xs opacity-75">{category.desc}</div>
              </div>
            ))}
          </div>
          
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <Lightbulb className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-blue-900 mb-1">💡 Pro Tip</h4>
                <p className="text-sm text-blue-800">
                  Don't worry if you're not sure - you can always skip this step and describe your website from scratch!
                </p>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: "description",
      title: "Describe Your Website",
      description: "The more details you provide, the better your website will be",
      icon: Lightbulb,
      content: (
        <div className="space-y-4">
          <div className="text-center mb-6">
            <Lightbulb className="h-12 w-12 text-slate-600 mx-auto mb-3" />
            <h3 className="text-lg font-semibold text-slate-900 mb-2">Tell us about your website</h3>
            <p className="text-slate-600">
              Describe what you want in plain English. Include colors, layout, sections, and functionality.
            </p>
          </div>
          
          <div className="bg-slate-50 border border-slate-200 rounded-lg p-4">
            <h4 className="font-medium text-slate-900 mb-2">Good example:</h4>
            <p className="text-sm text-slate-700 italic">
              "A modern portfolio website for a UX designer with a clean white background, 
              blue accent colors, a hero section with my photo, a project gallery with hover effects, 
              an about section, and a contact form."
            </p>
          </div>
          
          <div className="space-y-3">
            <h4 className="font-medium text-slate-900">Include these details for best results:</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
              {[
                "Industry or purpose",
                "Visual style preferences",
                "Color scheme",
                "Required sections",
                "Specific functionality",
                "Target audience"
              ].map((item) => (
                <div key={item} className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600 flex-shrink-0" />
                  <span className="text-slate-700">{item}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )
    },
    {
      id: "features",
      title: "Additional Features",
      description: "Learn about reference images and URLs to enhance your website",
      icon: Zap,
      content: (
        <div className="space-y-4">
          <div className="text-center mb-6">
            <Zap className="h-12 w-12 text-slate-600 mx-auto mb-3" />
            <h3 className="text-lg font-semibold text-slate-900 mb-2">Boost your results</h3>
            <p className="text-slate-600">
              Use these optional features to get even better results from our AI.
            </p>
          </div>
          
          <div className="space-y-4">
            <div className="border border-slate-200 rounded-lg p-4">
              <h4 className="font-semibold text-slate-900 mb-2">📸 Reference Screenshots</h4>
              <p className="text-sm text-slate-600 mb-3">
                Upload images of websites you like to inspire the design. Our AI will analyze the style and layout.
              </p>
              <div className="bg-green-50 border border-green-200 rounded p-3">
                <p className="text-sm text-green-800">
                  <strong>Tip:</strong> Screenshots from similar websites in your industry work best!
                </p>
              </div>
            </div>
            
            <div className="border border-slate-200 rounded-lg p-4">
              <h4 className="font-semibold text-slate-900 mb-2">🔗 Reference URLs</h4>
              <p className="text-sm text-slate-600 mb-3">
                Add links to websites you admire. We'll analyze their design patterns and functionality.
              </p>
              <div className="bg-blue-50 border border-blue-200 rounded p-3">
                <p className="text-sm text-blue-800">
                  <strong>Example:</strong> Add competitor websites or sites with layouts you love.
                </p>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: "ready",
      title: "You're All Set! 🚀",
      description: "Ready to create your first website with SiteForge",
      icon: CheckCircle,
      content: (
        <div className="space-y-4">
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="h-8 w-8 text-white" />
            </div>
            <h3 className="text-xl font-semibold text-slate-900 mb-2">You're ready to go!</h3>
            <p className="text-slate-600 mb-6">
              You now know everything you need to create amazing websites with SiteForge.
            </p>
          </div>
          
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-6">
            <h4 className="font-semibold text-slate-900 mb-3">🎯 Quick Recap:</h4>
            <div className="space-y-2 text-sm text-slate-700">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span>Choose a category (or skip if unsure)</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span>Describe your website in detail</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span>Add reference images/URLs (optional)</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span>Click "Choose Tech Stack" to continue</span>
              </div>
            </div>
          </div>
          
          <div className="text-center">
            <Button 
              onClick={onComplete}
              size="lg"
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
            >
              <Play className="mr-2 h-5 w-5" />
              Start Creating My Website
            </Button>
          </div>
        </div>
      )
    }
  ]

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCompletedSteps([...completedSteps, steps[currentStep].id])
      setCurrentStep(currentStep + 1)
    }
  }

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleStepClick = (stepIndex: number) => {
    setCurrentStep(stepIndex)
  }

  useEffect(() => {
    // Mark first-time user flag
    if (isOpen) {
      localStorage.setItem('siteforge-onboarding-shown', 'true')
    }
  }, [isOpen])

  if (!isOpen) return null

  const currentStepData = steps[currentStep]
  const Icon = currentStepData.icon

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <CardHeader className="relative">
          <button
            onClick={onClose}
            className="absolute top-4 right-4 p-2 hover:bg-slate-100 rounded-full transition-colors"
          >
            <X className="h-5 w-5 text-slate-500" />
          </button>
          
          {/* Progress indicator */}
          <div className="flex items-center justify-center mb-6">
            <div className="flex items-center space-x-2">
              {steps.map((step, index) => (
                <div key={step.id} className="flex items-center">
                  <button
                    onClick={() => handleStepClick(index)}
                    className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-colors ${
                      index === currentStep
                        ? 'bg-blue-600 text-white'
                        : index < currentStep || completedSteps.includes(step.id)
                        ? 'bg-green-600 text-white'
                        : 'bg-slate-200 text-slate-600 hover:bg-slate-300'
                    }`}
                  >
                    {index < currentStep || completedSteps.includes(step.id) ? (
                      <CheckCircle className="h-4 w-4" />
                    ) : (
                      index + 1
                    )}
                  </button>
                  {index < steps.length - 1 && (
                    <div className={`w-8 h-0.5 mx-2 ${
                      index < currentStep ? 'bg-green-600' : 'bg-slate-200'
                    }`} />
                  )}
                </div>
              ))}
            </div>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center gap-3 mb-2">
              <Icon className="h-6 w-6 text-blue-600" />
              <CardTitle className="text-2xl">{currentStepData.title}</CardTitle>
            </div>
            <CardDescription className="text-lg">
              {currentStepData.description}
            </CardDescription>
          </div>
        </CardHeader>
        
        <CardContent className="px-8 pb-8">
          {currentStepData.content}
          
          {/* Navigation buttons */}
          <div className="flex items-center justify-between mt-8 pt-6 border-t border-slate-200">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentStep === 0}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Previous
            </Button>
            
            <div className="text-sm text-slate-500">
              Step {currentStep + 1} of {steps.length}
            </div>
            
            {currentStep < steps.length - 1 ? (
              <Button onClick={handleNext} className="flex items-center gap-2">
                Next
                <ArrowRight className="h-4 w-4" />
              </Button>
            ) : (
              <Button 
                onClick={onComplete}
                className="bg-green-600 hover:bg-green-700 flex items-center gap-2"
              >
                <CheckCircle className="h-4 w-4" />
                Get Started
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
