"use client"

import { useState, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { 
  GripVertical, 
  Plus, 
  Trash2, 
  Edit3, 
  Eye, 
  Image as ImageIcon, 
  Type, 
  Layout, 
  Mail, 
  Star,
  Users,
  ShoppingCart,
  FileText,
  Video,
  Map,
  Phone
} from "lucide-react"

interface Section {
  id: string
  type: 'hero' | 'about' | 'services' | 'gallery' | 'testimonials' | 'contact' | 'footer' | 'features' | 'pricing' | 'team' | 'blog' | 'video' | 'map'
  title: string
  content: string
  order: number
  isEditing?: boolean
}

interface SectionBuilderProps {
  onSectionsChange: (sections: Section[]) => void
  initialSections?: Section[]
}

const sectionTypes = [
  { 
    type: 'hero' as const, 
    name: '<PERSON> <PERSON>', 
    icon: Layout, 
    description: 'Main banner with headline and call-to-action',
    defaultContent: 'Welcome to our amazing website. We provide exceptional services that will transform your business.'
  },
  { 
    type: 'about' as const, 
    name: 'About Us', 
    icon: Users, 
    description: 'Company or personal information',
    defaultContent: 'Learn about our story, mission, and the passionate team behind our success.'
  },
  { 
    type: 'services' as const, 
    name: 'Services', 
    icon: Star, 
    description: 'List of services or products offered',
    defaultContent: 'Discover our comprehensive range of services designed to meet your unique needs.'
  },
  { 
    type: 'features' as const, 
    name: 'Features', 
    icon: Star, 
    description: 'Key features or benefits',
    defaultContent: 'Explore the powerful features that make our solution stand out from the competition.'
  },
  { 
    type: 'gallery' as const, 
    name: 'Gallery', 
    icon: ImageIcon, 
    description: 'Image gallery or portfolio',
    defaultContent: 'Browse through our stunning collection of work and see what we can create for you.'
  },
  { 
    type: 'testimonials' as const, 
    name: 'Testimonials', 
    icon: Star, 
    description: 'Customer reviews and feedback',
    defaultContent: 'Read what our satisfied customers have to say about their experience with us.'
  },
  { 
    type: 'team' as const, 
    name: 'Our Team', 
    icon: Users, 
    description: 'Team member profiles',
    defaultContent: 'Meet the talented individuals who make our company exceptional.'
  },
  { 
    type: 'pricing' as const, 
    name: 'Pricing', 
    icon: ShoppingCart, 
    description: 'Pricing plans or packages',
    defaultContent: 'Choose the perfect plan that fits your needs and budget.'
  },
  { 
    type: 'blog' as const, 
    name: 'Blog', 
    icon: FileText, 
    description: 'Latest news and articles',
    defaultContent: 'Stay updated with our latest insights, tips, and industry news.'
  },
  { 
    type: 'video' as const, 
    name: 'Video', 
    icon: Video, 
    description: 'Embedded video content',
    defaultContent: 'Watch our introduction video to learn more about what we do.'
  },
  { 
    type: 'map' as const, 
    name: 'Location', 
    icon: Map, 
    description: 'Map and location information',
    defaultContent: 'Find us at our convenient location or contact us for directions.'
  },
  { 
    type: 'contact' as const, 
    name: 'Contact', 
    icon: Mail, 
    description: 'Contact form and information',
    defaultContent: 'Get in touch with us. We\'d love to hear from you and discuss your project.'
  },
  { 
    type: 'footer' as const, 
    name: 'Footer', 
    icon: Layout, 
    description: 'Footer with links and information',
    defaultContent: 'Copyright information, social links, and additional navigation.'
  }
]

export function SectionBuilder({ onSectionsChange, initialSections = [] }: SectionBuilderProps) {
  const [sections, setSections] = useState<Section[]>(initialSections.length > 0 ? initialSections : [
    {
      id: '1',
      type: 'hero',
      title: 'Hero Section',
      content: 'Welcome to our amazing website. We provide exceptional services that will transform your business.',
      order: 0
    }
  ])
  const [draggedItem, setDraggedItem] = useState<string | null>(null)
  const [showAddMenu, setShowAddMenu] = useState(false)
  const dragCounter = useRef(0)

  const handleDragStart = (e: React.DragEvent, sectionId: string) => {
    setDraggedItem(sectionId)
    e.dataTransfer.effectAllowed = 'move'
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    e.dataTransfer.dropEffect = 'move'
  }

  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault()
    dragCounter.current++
  }

  const handleDragLeave = (e: React.DragEvent) => {
    dragCounter.current--
  }

  const handleDrop = (e: React.DragEvent, targetSectionId: string) => {
    e.preventDefault()
    dragCounter.current = 0
    
    if (!draggedItem || draggedItem === targetSectionId) return

    const newSections = [...sections]
    const draggedIndex = newSections.findIndex(s => s.id === draggedItem)
    const targetIndex = newSections.findIndex(s => s.id === targetSectionId)

    if (draggedIndex === -1 || targetIndex === -1) return

    // Remove dragged item and insert at target position
    const [draggedSection] = newSections.splice(draggedIndex, 1)
    newSections.splice(targetIndex, 0, draggedSection)

    // Update order
    const updatedSections = newSections.map((section, index) => ({
      ...section,
      order: index
    }))

    setSections(updatedSections)
    onSectionsChange(updatedSections)
    setDraggedItem(null)
  }

  const addSection = (type: Section['type']) => {
    const sectionType = sectionTypes.find(st => st.type === type)
    if (!sectionType) return

    const newSection: Section = {
      id: Date.now().toString(),
      type,
      title: sectionType.name,
      content: sectionType.defaultContent,
      order: sections.length
    }

    const updatedSections = [...sections, newSection]
    setSections(updatedSections)
    onSectionsChange(updatedSections)
    setShowAddMenu(false)
  }

  const removeSection = (sectionId: string) => {
    const updatedSections = sections
      .filter(s => s.id !== sectionId)
      .map((section, index) => ({ ...section, order: index }))
    
    setSections(updatedSections)
    onSectionsChange(updatedSections)
  }

  const updateSection = (sectionId: string, updates: Partial<Section>) => {
    const updatedSections = sections.map(section =>
      section.id === sectionId ? { ...section, ...updates } : section
    )
    
    setSections(updatedSections)
    onSectionsChange(updatedSections)
  }

  const toggleEdit = (sectionId: string) => {
    updateSection(sectionId, { isEditing: !sections.find(s => s.id === sectionId)?.isEditing })
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-slate-900">Website Sections</h3>
          <p className="text-sm text-slate-600">Drag and drop to reorder sections</p>
        </div>
        <div className="relative">
          <Button
            onClick={() => setShowAddMenu(!showAddMenu)}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Add Section
          </Button>
          
          {showAddMenu && (
            <div className="absolute right-0 top-full mt-2 w-80 bg-white border border-slate-200 rounded-lg shadow-lg z-10 max-h-96 overflow-y-auto">
              <div className="p-4">
                <h4 className="font-medium text-slate-900 mb-3">Choose Section Type</h4>
                <div className="grid grid-cols-1 gap-2">
                  {sectionTypes.map((sectionType) => {
                    const Icon = sectionType.icon
                    return (
                      <button
                        key={sectionType.type}
                        onClick={() => addSection(sectionType.type)}
                        className="flex items-start gap-3 p-3 text-left hover:bg-slate-50 rounded-lg transition-colors"
                      >
                        <Icon className="h-5 w-5 text-slate-600 mt-0.5 flex-shrink-0" />
                        <div>
                          <div className="font-medium text-slate-900 text-sm">{sectionType.name}</div>
                          <div className="text-xs text-slate-600">{sectionType.description}</div>
                        </div>
                      </button>
                    )
                  })}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="space-y-4">
        {sections.map((section) => {
          const sectionType = sectionTypes.find(st => st.type === section.type)
          const Icon = sectionType?.icon || Layout

          return (
            <Card
              key={section.id}
              className={`transition-all duration-200 ${
                draggedItem === section.id ? 'opacity-50 scale-95' : 'hover:shadow-md'
              }`}
              draggable
              onDragStart={(e) => handleDragStart(e, section.id)}
              onDragOver={handleDragOver}
              onDragEnter={handleDragEnter}
              onDragLeave={handleDragLeave}
              onDrop={(e) => handleDrop(e, section.id)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="cursor-grab active:cursor-grabbing">
                      <GripVertical className="h-5 w-5 text-slate-400" />
                    </div>
                    <Icon className="h-5 w-5 text-slate-600" />
                    {section.isEditing ? (
                      <Input
                        value={section.title}
                        onChange={(e) => updateSection(section.id, { title: e.target.value })}
                        className="font-medium"
                        onBlur={() => toggleEdit(section.id)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            toggleEdit(section.id)
                          }
                        }}
                        autoFocus
                      />
                    ) : (
                      <CardTitle 
                        className="text-lg cursor-pointer hover:text-slate-700"
                        onClick={() => toggleEdit(section.id)}
                      >
                        {section.title}
                      </CardTitle>
                    )}
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleEdit(section.id)}
                      className="text-slate-500 hover:text-slate-700"
                    >
                      <Edit3 className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeSection(section.id)}
                      className="text-slate-500 hover:text-red-600"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent>
                {section.isEditing ? (
                  <Textarea
                    value={section.content}
                    onChange={(e) => updateSection(section.id, { content: e.target.value })}
                    className="min-h-[100px]"
                    placeholder="Describe what this section should contain..."
                  />
                ) : (
                  <p className="text-slate-600 cursor-pointer hover:text-slate-800" onClick={() => toggleEdit(section.id)}>
                    {section.content || 'Click to add content...'}
                  </p>
                )}
                
                <div className="mt-3 text-xs text-slate-500">
                  {sectionType?.description}
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {sections.length === 0 && (
        <div className="text-center py-12 border-2 border-dashed border-slate-300 rounded-lg">
          <Layout className="h-12 w-12 text-slate-400 mx-auto mb-4" />
          <h4 className="text-lg font-medium text-slate-900 mb-2">No sections yet</h4>
          <p className="text-slate-600 mb-4">Add your first section to get started</p>
          <Button onClick={() => setShowAddMenu(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add Section
          </Button>
        </div>
      )}
      
      {showAddMenu && (
        <div 
          className="fixed inset-0 z-5" 
          onClick={() => setShowAddMenu(false)}
        />
      )}
    </div>
  )
}
