"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  Palette, 
  Type, 
  Square, 
  Plus, 
  Copy,
  Trash2,
  MousePointer,
  Image as ImageIcon
} from "lucide-react"

interface DesignSystem {
  components: any[]
  colors: string[]
  typography: any[]
}

interface DesignSystemPanelProps {
  designSystem: DesignSystem
  onUpdateDesignSystem: (updates: Partial<DesignSystem>) => void
  onApplyComponent: (component: any) => void
}

const predefinedComponents = [
  {
    id: 'hero-section',
    name: 'Hero Section',
    type: 'section',
    content: 'Hero Section',
    styles: {
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      color: 'white',
      padding: '4rem 2rem',
      textAlign: 'center'
    },
    preview: 'Large header with background'
  },
  {
    id: 'feature-card',
    name: 'Feature Card',
    type: 'container',
    content: 'Feature Card',
    styles: {
      background: 'white',
      padding: '2rem',
      borderRadius: '10px',
      boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
      textAlign: 'center'
    },
    preview: 'Card with shadow and padding'
  },
  {
    id: 'cta-button',
    name: 'CTA Button',
    type: 'button',
    content: 'Get Started',
    styles: {
      background: '#667eea',
      color: 'white',
      padding: '1rem 2rem',
      borderRadius: '50px',
      border: 'none',
      fontWeight: 'bold',
      cursor: 'pointer'
    },
    preview: 'Primary action button'
  },
  {
    id: 'text-block',
    name: 'Text Block',
    type: 'text',
    content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
    styles: {
      fontSize: '1rem',
      lineHeight: '1.6',
      color: '#333'
    },
    preview: 'Standard text paragraph'
  }
]

const colorPalettes = [
  { name: 'Blue Gradient', colors: ['#667eea', '#764ba2', '#ffffff', '#333333'] },
  { name: 'Green Nature', colors: ['#10b981', '#059669', '#f0fdf4', '#1f2937'] },
  { name: 'Purple Modern', colors: ['#8b5cf6', '#7c3aed', '#faf5ff', '#374151'] },
  { name: 'Orange Warm', colors: ['#f97316', '#ea580c', '#fff7ed', '#1f2937'] },
  { name: 'Pink Creative', colors: ['#ec4899', '#db2777', '#fdf2f8', '#374151'] },
  { name: 'Monochrome', colors: ['#000000', '#6b7280', '#f9fafb', '#111827'] }
]

export function DesignSystemPanel({
  designSystem,
  onUpdateDesignSystem,
  onApplyComponent
}: DesignSystemPanelProps) {
  const [newColor, setNewColor] = useState('#667eea')

  const addColor = () => {
    if (!designSystem.colors.includes(newColor)) {
      onUpdateDesignSystem({
        colors: [...designSystem.colors, newColor]
      })
    }
  }

  const removeColor = (color: string) => {
    onUpdateDesignSystem({
      colors: designSystem.colors.filter(c => c !== color)
    })
  }

  const applyColorPalette = (palette: string[]) => {
    onUpdateDesignSystem({
      colors: palette
    })
  }

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b border-slate-200">
        <div className="flex items-center space-x-2">
          <Palette className="h-4 w-4 text-slate-600" />
          <h3 className="font-semibold text-slate-900">Design System</h3>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto">
        <Tabs defaultValue="components" className="h-full">
          <TabsList className="grid w-full grid-cols-3 m-2">
            <TabsTrigger value="components" className="text-xs">
              <Square className="h-3 w-3 mr-1" />
              Components
            </TabsTrigger>
            <TabsTrigger value="colors" className="text-xs">
              <Palette className="h-3 w-3 mr-1" />
              Colors
            </TabsTrigger>
            <TabsTrigger value="typography" className="text-xs">
              <Type className="h-3 w-3 mr-1" />
              Typography
            </TabsTrigger>
          </TabsList>

          <TabsContent value="components" className="p-4 space-y-4">
            <div>
              <h4 className="text-sm font-medium text-slate-900 mb-3">Predefined Components</h4>
              <div className="space-y-2">
                {predefinedComponents.map((component) => (
                  <Card key={component.id} className="cursor-pointer border border-slate-200">
                    <CardContent className="p-3">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <h5 className="text-sm font-medium text-slate-900">{component.name}</h5>
                          <p className="text-xs text-slate-500">{component.preview}</p>
                        </div>
                        <Button
                          size="sm"
                          onClick={() => onApplyComponent(component)}
                          className="ml-2"
                        >
                          <Plus className="h-3 w-3" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium text-slate-900 mb-3">Custom Components</h4>
              {designSystem.components.length === 0 ? (
                <div className="text-center py-4 text-slate-500">
                  <Square className="h-6 w-6 mx-auto mb-2 opacity-50" />
                  <p className="text-xs">No custom components yet</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {designSystem.components.map((component, index) => (
                    <Card key={index} className="border border-slate-200">
                      <CardContent className="p-3">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <h5 className="text-sm font-medium text-slate-900">{component.name}</h5>
                            <p className="text-xs text-slate-500">{component.type}</p>
                          </div>
                          <div className="flex space-x-1">
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => onApplyComponent(component)}
                              className="p-1"
                            >
                              <Copy className="h-3 w-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              className="p-1 text-red-600"
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="colors" className="p-4 space-y-4">
            <div>
              <h4 className="text-sm font-medium text-slate-900 mb-3">Color Palettes</h4>
              <div className="space-y-2">
                {colorPalettes.map((palette) => (
                  <div key={palette.name} className="p-2 border border-slate-200 rounded">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-xs font-medium text-slate-900">{palette.name}</span>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => applyColorPalette(palette.colors)}
                        className="text-xs px-2 py-1"
                      >
                        Apply
                      </Button>
                    </div>
                    <div className="flex space-x-1">
                      {palette.colors.map((color, index) => (
                        <div
                          key={index}
                          className="w-6 h-6 rounded border border-slate-300"
                          style={{ backgroundColor: color }}
                        />
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium text-slate-900 mb-3">Current Colors</h4>
              <div className="flex items-center space-x-2 mb-3">
                <Input
                  type="color"
                  value={newColor}
                  onChange={(e) => setNewColor(e.target.value)}
                  className="w-12 h-8 p-0 border-0"
                />
                <Input
                  type="text"
                  value={newColor}
                  onChange={(e) => setNewColor(e.target.value)}
                  className="flex-1 text-xs"
                  placeholder="#667eea"
                />
                <Button size="sm" onClick={addColor}>
                  <Plus className="h-3 w-3" />
                </Button>
              </div>
              
              <div className="grid grid-cols-4 gap-2">
                {designSystem.colors.map((color, index) => (
                  <div key={index} className="relative group">
                    <div
                      className="w-full h-12 rounded border border-slate-300 cursor-pointer"
                      style={{ backgroundColor: color }}
                      title={color}
                    />
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => removeColor(color)}
                      className="absolute top-0 right-0 p-1 opacity-0 group-hover:opacity-100 bg-white shadow-sm"
                    >
                      <Trash2 className="h-3 w-3 text-red-600" />
                    </Button>
                    <div className="text-xs text-center mt-1 text-slate-600">{color}</div>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="typography" className="p-4 space-y-4">
            <div className="text-center py-8 text-slate-500">
              <Type className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">Typography system coming soon</p>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
