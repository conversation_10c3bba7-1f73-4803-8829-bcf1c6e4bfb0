"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { 
  Settings, 
  Palette, 
  Move, 
  Type,
  Image as ImageIcon,
  Wand2,
  Upload,
  Play
} from "lucide-react"

interface WebsiteElement {
  id: string
  type: 'text' | 'image' | 'button' | 'section' | 'container' | 'header' | 'footer'
  content: string
  styles: Record<string, string>
  position: { x: number; y: number; width: number; height: number }
  animations?: {
    type: 'fade' | 'slide' | 'scale' | 'none'
    duration: number
    delay: number
  }
  borders?: {
    width: string
    style: string
    color: string
    radius: string
  }
  shadows?: {
    x: string
    y: string
    blur: string
    spread: string
    color: string
  }
}

interface WebsiteState {
  pages: any[]
  currentPageId: string
  globalStyles: {
    backgroundColor: string
    primaryColor: string
    secondaryColor: string
    textColor: string
    fontFamily: string
    fontSize: string
  }
  layout: {
    spacing: string
    padding: string
    margin: string
  }
  designSystem: any
  grid: any
}

interface PropertiesPanelProps {
  selectedElement: WebsiteElement | null
  websiteState: WebsiteState
  onUpdateElement: (elementId: string, updates: Partial<WebsiteElement>) => void
  onUpdateGlobalStyles: (updates: Partial<WebsiteState['globalStyles']>) => void
  onUpdateLayout: (updates: Partial<WebsiteState['layout']>) => void
  onApplyEdit: (command: string) => void
  isProcessing: boolean
}

const animationTypes = [
  { value: 'none', label: 'None' },
  { value: 'fade', label: 'Fade In' },
  { value: 'slide', label: 'Slide In' },
  { value: 'scale', label: 'Scale In' }
]

const borderStyles = [
  { value: 'solid', label: 'Solid' },
  { value: 'dashed', label: 'Dashed' },
  { value: 'dotted', label: 'Dotted' },
  { value: 'none', label: 'None' }
]

export function PropertiesPanel({
  selectedElement,
  websiteState,
  onUpdateElement,
  onUpdateGlobalStyles,
  onUpdateLayout,
  onApplyEdit,
  isProcessing
}: PropertiesPanelProps) {
  const [command, setCommand] = useState("")

  const handleQuickEdit = () => {
    if (!command.trim()) return
    onApplyEdit(command)
    setCommand("")
  }

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file && selectedElement) {
      // In a real app, you'd upload the file and get a URL
      const imageUrl = URL.createObjectURL(file)
      onUpdateElement(selectedElement.id, {
        content: imageUrl,
        styles: { ...selectedElement.styles, backgroundImage: `url(${imageUrl})` }
      })
    }
  }

  if (!selectedElement) {
    return (
      <div className="h-full flex flex-col">
        <div className="p-4 border-b border-white/20">
          <div className="flex items-center space-x-2">
            <Settings className="h-4 w-4 text-slate-600" />
            <h3 className="font-semibold text-slate-900">Properties</h3>
          </div>
        </div>
        
        <div className="flex-1 p-4">
          <div className="text-center py-8 text-slate-500">
            <Settings className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">Select an element to edit its properties</p>
          </div>

          {/* Quick Edit */}
          <Card className="mt-4 bg-white/60 backdrop-blur-sm border border-white/30 shadow-lg shadow-slate-200/40">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center">
                <Wand2 className="h-4 w-4 mr-2" />
                Quick Edit
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <Textarea
                  placeholder="Describe changes in plain English..."
                  value={command}
                  onChange={(e) => setCommand(e.target.value)}
                  className="min-h-[60px] text-sm"
                  disabled={isProcessing}
                />
                <Button
                  onClick={handleQuickEdit}
                  disabled={!command.trim() || isProcessing}
                  className="w-full"
                  size="sm"
                >
                  {isProcessing ? (
                    <>
                      <Wand2 className="mr-2 h-4 w-4 animate-pulse" />
                      Applying...
                    </>
                  ) : (
                    <>
                      Apply Changes
                      <Wand2 className="ml-2 h-4 w-4" />
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b border-white/20">
        <div className="flex items-center space-x-2">
          <Settings className="h-4 w-4 text-slate-600" />
          <h3 className="font-semibold text-slate-900">Properties</h3>
        </div>
        <p className="text-xs text-slate-500 mt-1 bg-white/50 backdrop-blur-sm px-2 py-1 rounded-full border border-white/30 inline-block">Editing: {selectedElement.id}</p>
      </div>

      <div className="flex-1 overflow-y-auto">
        <Tabs defaultValue="content" className="h-full">
          <TabsList className="grid w-full grid-cols-4 m-2">
            <TabsTrigger value="content" className="text-xs">
              <Type className="h-3 w-3 mr-1" />
              Content
            </TabsTrigger>
            <TabsTrigger value="style" className="text-xs">
              <Palette className="h-3 w-3 mr-1" />
              Style
            </TabsTrigger>
            <TabsTrigger value="position" className="text-xs">
              <Move className="h-3 w-3 mr-1" />
              Position
            </TabsTrigger>
            <TabsTrigger value="effects" className="text-xs">
              <Play className="h-3 w-3 mr-1" />
              Effects
            </TabsTrigger>
          </TabsList>

          <TabsContent value="content" className="p-4 space-y-4">
            <div>
              <Label className="text-sm font-medium">Content</Label>
              {selectedElement.type === 'image' ? (
                <div className="mt-2 space-y-2">
                  <div className="border-2 border-dashed border-slate-300 rounded-lg p-4 text-center">
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleImageUpload}
                      className="hidden"
                      id="image-upload"
                    />
                    <label htmlFor="image-upload" className="cursor-pointer">
                      <Upload className="h-6 w-6 text-slate-400 mx-auto mb-2" />
                      <p className="text-sm text-slate-600">Upload new image</p>
                    </label>
                  </div>
                  <Input
                    value={selectedElement.content}
                    onChange={(e) => onUpdateElement(selectedElement.id, { content: e.target.value })}
                    placeholder="Image URL"
                    className="text-sm"
                  />
                </div>
              ) : (
                <Textarea
                  value={selectedElement.content}
                  onChange={(e) => onUpdateElement(selectedElement.id, { content: e.target.value })}
                  className="mt-2 text-sm"
                  rows={3}
                />
              )}
            </div>

            <div>
              <Label className="text-sm font-medium">Element Type</Label>
              <select
                value={selectedElement.type}
                onChange={(e) => onUpdateElement(selectedElement.id, { type: e.target.value as any })}
                className="w-full mt-2 p-2 border border-slate-200 rounded text-sm"
              >
                <option value="text">Text</option>
                <option value="image">Image</option>
                <option value="button">Button</option>
                <option value="section">Section</option>
                <option value="container">Container</option>
              </select>
            </div>
          </TabsContent>

          <TabsContent value="style" className="p-4 space-y-4">
            <div className="grid grid-cols-2 gap-3">
              <div>
                <Label className="text-sm font-medium">Background</Label>
                <Input
                  type="color"
                  value={selectedElement.styles.backgroundColor || '#ffffff'}
                  onChange={(e) => onUpdateElement(selectedElement.id, {
                    styles: { ...selectedElement.styles, backgroundColor: e.target.value }
                  })}
                  className="mt-1 h-8"
                />
              </div>
              <div>
                <Label className="text-sm font-medium">Text Color</Label>
                <Input
                  type="color"
                  value={selectedElement.styles.color || '#333333'}
                  onChange={(e) => onUpdateElement(selectedElement.id, {
                    styles: { ...selectedElement.styles, color: e.target.value }
                  })}
                  className="mt-1 h-8"
                />
              </div>
            </div>

            <div>
              <Label className="text-sm font-medium">Font Size</Label>
              <Input
                value={selectedElement.styles.fontSize || '16px'}
                onChange={(e) => onUpdateElement(selectedElement.id, {
                  styles: { ...selectedElement.styles, fontSize: e.target.value }
                })}
                className="mt-1 text-sm"
                placeholder="16px"
              />
            </div>

            <div>
              <Label className="text-sm font-medium">Padding</Label>
              <Input
                value={selectedElement.styles.padding || '0px'}
                onChange={(e) => onUpdateElement(selectedElement.id, {
                  styles: { ...selectedElement.styles, padding: e.target.value }
                })}
                className="mt-1 text-sm"
                placeholder="1rem"
              />
            </div>

            <div>
              <Label className="text-sm font-medium">Border Radius</Label>
              <Input
                value={selectedElement.borders?.radius || '0px'}
                onChange={(e) => onUpdateElement(selectedElement.id, {
                  borders: { ...selectedElement.borders, radius: e.target.value }
                })}
                className="mt-1 text-sm"
                placeholder="8px"
              />
            </div>
          </TabsContent>

          <TabsContent value="position" className="p-4 space-y-4">
            <div className="grid grid-cols-2 gap-3">
              <div>
                <Label className="text-sm font-medium">X Position</Label>
                <Input
                  type="number"
                  value={selectedElement.position.x}
                  onChange={(e) => onUpdateElement(selectedElement.id, {
                    position: { ...selectedElement.position, x: Number(e.target.value) }
                  })}
                  className="mt-1 text-sm"
                />
              </div>
              <div>
                <Label className="text-sm font-medium">Y Position</Label>
                <Input
                  type="number"
                  value={selectedElement.position.y}
                  onChange={(e) => onUpdateElement(selectedElement.id, {
                    position: { ...selectedElement.position, y: Number(e.target.value) }
                  })}
                  className="mt-1 text-sm"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div>
                <Label className="text-sm font-medium">Width</Label>
                <Input
                  type="number"
                  value={selectedElement.position.width}
                  onChange={(e) => onUpdateElement(selectedElement.id, {
                    position: { ...selectedElement.position, width: Number(e.target.value) }
                  })}
                  className="mt-1 text-sm"
                />
              </div>
              <div>
                <Label className="text-sm font-medium">Height</Label>
                <Input
                  type="number"
                  value={selectedElement.position.height}
                  onChange={(e) => onUpdateElement(selectedElement.id, {
                    position: { ...selectedElement.position, height: Number(e.target.value) }
                  })}
                  className="mt-1 text-sm"
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="effects" className="p-4 space-y-4">
            <div>
              <Label className="text-sm font-medium">Animation</Label>
              <select
                value={selectedElement.animations?.type || 'none'}
                onChange={(e) => onUpdateElement(selectedElement.id, {
                  animations: { 
                    ...selectedElement.animations, 
                    type: e.target.value as any 
                  }
                })}
                className="w-full mt-2 p-2 border border-slate-200 rounded text-sm"
              >
                {animationTypes.map(type => (
                  <option key={type.value} value={type.value}>{type.label}</option>
                ))}
              </select>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div>
                <Label className="text-sm font-medium">Duration (ms)</Label>
                <Input
                  type="number"
                  value={selectedElement.animations?.duration || 1000}
                  onChange={(e) => onUpdateElement(selectedElement.id, {
                    animations: { 
                      ...selectedElement.animations, 
                      duration: Number(e.target.value) 
                    }
                  })}
                  className="mt-1 text-sm"
                />
              </div>
              <div>
                <Label className="text-sm font-medium">Delay (ms)</Label>
                <Input
                  type="number"
                  value={selectedElement.animations?.delay || 0}
                  onChange={(e) => onUpdateElement(selectedElement.id, {
                    animations: { 
                      ...selectedElement.animations, 
                      delay: Number(e.target.value) 
                    }
                  })}
                  className="mt-1 text-sm"
                />
              </div>
            </div>

            <div>
              <Label className="text-sm font-medium">Box Shadow</Label>
              <div className="grid grid-cols-2 gap-2 mt-2">
                <Input
                  placeholder="X offset"
                  value={selectedElement.shadows?.x || '0px'}
                  onChange={(e) => onUpdateElement(selectedElement.id, {
                    shadows: { ...selectedElement.shadows, x: e.target.value }
                  })}
                  className="text-xs"
                />
                <Input
                  placeholder="Y offset"
                  value={selectedElement.shadows?.y || '0px'}
                  onChange={(e) => onUpdateElement(selectedElement.id, {
                    shadows: { ...selectedElement.shadows, y: e.target.value }
                  })}
                  className="text-xs"
                />
                <Input
                  placeholder="Blur"
                  value={selectedElement.shadows?.blur || '0px'}
                  onChange={(e) => onUpdateElement(selectedElement.id, {
                    shadows: { ...selectedElement.shadows, blur: e.target.value }
                  })}
                  className="text-xs"
                />
                <Input
                  type="color"
                  value={selectedElement.shadows?.color || '#000000'}
                  onChange={(e) => onUpdateElement(selectedElement.id, {
                    shadows: { ...selectedElement.shadows, color: e.target.value }
                  })}
                  className="text-xs h-8"
                />
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
