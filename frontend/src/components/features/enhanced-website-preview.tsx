"use client"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Monitor, Smartphone, Tablet, RotateCcw, Edit<PERSON>, <PERSON><PERSON>ointer, Maximize } from "lucide-react"

interface WebsiteElement {
  id: string
  type: 'text' | 'image' | 'button' | 'section'
  content: string
  styles: Record<string, string>
  position: { x: number; y: number; width: number; height: number }
}

interface WebsiteState {
  elements: WebsiteElement[]
  globalStyles: {
    backgroundColor: string
    primaryColor: string
    secondaryColor: string
    textColor: string
    fontFamily: string
    fontSize: string
  }
  layout: {
    spacing: string
    padding: string
    margin: string
  }
}

interface WebsitePage {
  id: string
  name: string
  title: string
  slug: string
  metaDescription: string
  elements: WebsiteElement[]
  template?: 'home' | 'about' | 'contact' | 'services' | 'portfolio' | 'blog' | 'custom'
}

interface EnhancedWebsitePreviewProps {
  websiteState: any
  currentPage?: WebsitePage
  generatedCode: string
  techStack: 'html' | 'react'
  selectedElement: string | null
  selectedElements: string[]
  onSelectElement: (elementId: string | null) => void
  onSelectMultiple: (elementIds: string[]) => void
  onUpdateElement: (elementId: string, updates: Partial<WebsiteElement>) => void
  gridEnabled: boolean
  gridSize: number
  snapToGrid: boolean
}

type ViewportSize = 'desktop' | 'tablet' | 'mobile'

const viewportSizes = {
  desktop: { width: '100%', height: '700px', icon: Monitor, label: 'Desktop' },
  tablet: { width: '768px', height: '700px', icon: Tablet, label: 'Tablet' },
  mobile: { width: '375px', height: '700px', icon: Smartphone, label: 'Mobile' }
}

export function EnhancedWebsitePreview({
  websiteState,
  currentPage,
  generatedCode,
  techStack,
  selectedElement,
  selectedElements,
  onSelectElement,
  onSelectMultiple,
  onUpdateElement,
  gridEnabled,
  gridSize,
  snapToGrid
}: EnhancedWebsitePreviewProps) {
  const [viewport, setViewport] = useState<ViewportSize>('desktop')
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [editMode, setEditMode] = useState(false)
  const [editingText, setEditingText] = useState<string | null>(null)
  const [zoom, setZoom] = useState(100)
  const iframeRef = useRef<HTMLIFrameElement>(null)

  const createEnhancedContent = () => {
    try {
      if (!websiteState || !websiteState.globalStyles) {
        return `
          <!DOCTYPE html>
          <html lang="en">
          <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Loading...</title>
          </head>
          <body style="display: flex; align-items: center; justify-content: center; height: 100vh; font-family: Arial, sans-serif;">
            <div style="text-align: center;">
              <div style="margin-bottom: 10px;">Loading preview...</div>
              <div style="width: 20px; height: 20px; border: 2px solid #e2e8f0; border-top: 2px solid #64748b; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto;"></div>
            </div>
            <style>
              @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
            </style>
          </body>
          </html>
        `
      }

      const { globalStyles, layout } = websiteState
      const elements = currentPage?.elements || []

      return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>SiteForge Studio Preview</title>
        <style>
          * { box-sizing: border-box; }
          body {
            margin: 0;
            padding: 0;
            font-family: ${globalStyles.fontFamily};
            font-size: ${globalStyles.fontSize};
            color: ${globalStyles.textColor};
            background-color: ${globalStyles.backgroundColor};
            position: relative;
            min-height: 100vh;
          }

          /* Grid overlay */
          .grid-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1000;
            opacity: ${gridEnabled ? '0.3' : '0'};
            background-image:
              linear-gradient(to right, #e2e8f0 1px, transparent 1px),
              linear-gradient(to bottom, #e2e8f0 1px, transparent 1px);
            background-size: ${gridSize}px ${gridSize}px;
          }
          .editable-element {
            position: relative;
            cursor: pointer;
            transition: all 0.2s ease;
            min-height: 20px;
            min-width: 20px;
          }
          .editable-element:hover {
            outline: 2px dashed #3b82f6;
            outline-offset: 2px;
          }
          .editable-element.selected {
            outline: 2px solid #3b82f6;
            outline-offset: 2px;
          }
          .editable-element.multi-selected {
            outline: 2px solid #f59e0b;
            outline-offset: 2px;
          }
          .edit-overlay {
            position: absolute;
            top: -30px;
            left: 0;
            background: #3b82f6;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.2s ease;
          }
          .editable-element:hover .edit-overlay,
          .editable-element.selected .edit-overlay {
            opacity: 1;
          }

          /* Element positioning */
          .positioned-element {
            position: absolute;
          }

          /* Animation classes */
          .animate-fade { animation: fadeIn 1s ease-in-out; }
          .animate-slide { animation: slideIn 1s ease-in-out; }
          .animate-scale { animation: scaleIn 1s ease-in-out; }

          @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
          }

          @keyframes slideIn {
            from { transform: translateY(20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
          }

          @keyframes scaleIn {
            from { transform: scale(0.9); opacity: 0; }
            to { transform: scale(1); opacity: 1; }
          }
          header {
            background: linear-gradient(135deg, ${globalStyles.primaryColor} 0%, ${globalStyles.secondaryColor} 100%);
            color: white;
            padding: ${layout.padding};
            text-align: center;
            position: relative;
          }
          .hero-title {
            font-size: 3rem;
            margin: 0 0 1rem 0;
            font-weight: bold;
          }
          .hero-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin: 0 0 2rem 0;
          }
          .cta-button {
            background: white;
            color: ${globalStyles.primaryColor};
            border: none;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-weight: bold;
            cursor: pointer;
            font-size: 1rem;
            transition: transform 0.2s ease;
          }
          .features-section {
            padding: ${layout.padding};
            max-width: 1200px;
            margin: 0 auto;
          }
          .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
          }
          .feature-card {
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            text-align: center;
            background: white;
          }
          .feature-title {
            color: ${globalStyles.textColor};
            margin-bottom: 1rem;
            font-size: 1.25rem;
            font-weight: 600;
          }
          .feature-description {
            color: #666;
            line-height: 1.6;
          }
          footer {
            background: #333;
            color: white;
            text-align: center;
            padding: 2rem;
            margin-top: 4rem;
          }
        </style>
      </head>
      <body>
        <!-- Grid Overlay -->
        <div class="grid-overlay"></div>

        <!-- Initialize JavaScript variables -->
        <script>
          // Initialize global variables safely
          window.selectedElements = window.selectedElements || [];

          // Ensure DOM is ready before setting up event handlers
          if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
              console.log('DOM loaded, initializing preview...');
            });
          }
        </script>

        <!-- Dynamic Elements -->
        ${elements && elements.length > 0 ? elements.map(element => {
          if (!element || !element.id) return ''

          const isSelected = selectedElement === element.id
          const isMultiSelected = selectedElements && selectedElements.includes(element.id)
          const animationClass = element.animations?.type && element.animations.type !== 'none' ? `animate-${element.animations.type}` : ''

          // Ensure all required properties exist with defaults
          const position = element.position || { x: 0, y: 0, width: 100, height: 50 }
          const styles = element.styles || {}
          const borders = element.borders || { width: '0px', style: 'solid', color: 'transparent', radius: '0px' }
          const shadows = element.shadows || { x: '0px', y: '0px', blur: '0px', spread: '0px', color: 'transparent' }
          const animations = element.animations || { delay: 0, duration: 1000 }

          const elementStyles = {
            ...styles,
            position: 'absolute',
            left: `${position.x}px`,
            top: `${position.y}px`,
            width: `${position.width}px`,
            height: `${position.height}px`,
            border: `${borders.width} ${borders.style} ${borders.color}`,
            borderRadius: borders.radius,
            boxShadow: `${shadows.x} ${shadows.y} ${shadows.blur} ${shadows.spread} ${shadows.color}`,
            animationDelay: `${animations.delay}ms`,
            animationDuration: `${animations.duration}ms`
          }

          const styleString = Object.entries(elementStyles)
            .filter(([key, value]) => value !== undefined && value !== null)
            .map(([key, value]) => `${key.replace(/([A-Z])/g, '-$1').toLowerCase()}: ${value}`)
            .join('; ')

          const classes = [
            'editable-element',
            'positioned-element',
            animationClass,
            isSelected ? 'selected' : '',
            isMultiSelected ? 'multi-selected' : ''
          ].filter(Boolean).join(' ')

          return `
            <div
              class="${classes}"
              data-element-id="${element.id}"
              style="${styleString}"
              onclick="selectElement('${element.id}', event)"
            >
              ${element.content || ''}
              <div class="edit-overlay">${element.type || 'element'} • Click to edit</div>
            </div>
          `
        }).join('') : ''}
        
        <section class="features-section">
          <div class="features-grid">
            <div class="feature-card">
              <h3 class="editable-element feature-title" data-element-id="feature-1-title" onclick="selectElement('feature-1-title')">
                Feature One
                <div class="edit-overlay">Click to edit</div>
              </h3>
              <p class="editable-element feature-description" data-element-id="feature-1-desc" onclick="selectElement('feature-1-desc')">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt.
                <div class="edit-overlay">Click to edit</div>
              </p>
            </div>
            <div class="feature-card">
              <h3 class="editable-element feature-title" data-element-id="feature-2-title" onclick="selectElement('feature-2-title')">
                Feature Two
                <div class="edit-overlay">Click to edit</div>
              </h3>
              <p class="editable-element feature-description" data-element-id="feature-2-desc" onclick="selectElement('feature-2-desc')">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt.
                <div class="edit-overlay">Click to edit</div>
              </p>
            </div>
            <div class="feature-card">
              <h3 class="editable-element feature-title" data-element-id="feature-3-title" onclick="selectElement('feature-3-title')">
                Feature Three
                <div class="edit-overlay">Click to edit</div>
              </h3>
              <p class="editable-element feature-description" data-element-id="feature-3-desc" onclick="selectElement('feature-3-desc')">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt.
                <div class="edit-overlay">Click to edit</div>
              </p>
            </div>
          </div>
        </section>
        
        <footer>
          <p class="editable-element" data-element-id="footer-text" onclick="selectElement('footer-text')">
            &copy; 2024 Your Website. All rights reserved.
            <div class="edit-overlay">Click to edit</div>
          </p>
        </footer>

        <script>
          // Initialize variables
          let selectedElements = [];

          function selectElement(elementId, event) {
            if (!elementId) return;

            event = event || window.event;
            const isCtrlPressed = event && (event.ctrlKey || event.metaKey);

            // Remove previous selection classes
            document.querySelectorAll('.editable-element').forEach(function(el) {
              el.classList.remove('selected', 'multi-selected');
            });

            if (isCtrlPressed) {
              // Multi-selection with Ctrl/Cmd
              if (selectedElements.indexOf(elementId) !== -1) {
                selectedElements = selectedElements.filter(function(id) { return id !== elementId; });
              } else {
                selectedElements.push(elementId);
              }
            } else {
              // Single selection
              selectedElements = [elementId];
            }

            // Apply selection classes
            selectedElements.forEach(function(id) {
              const element = document.querySelector('[data-element-id="' + id + '"]');
              if (element) {
                if (selectedElements.length === 1) {
                  element.classList.add('selected');
                } else {
                  element.classList.add('multi-selected');
                }
              }
            });

            // Notify parent window
            if (window.parent && window.parent.postMessage) {
              window.parent.postMessage({
                type: 'elementSelected',
                elementId: selectedElements.length === 1 ? elementId : null,
                selectedElements: selectedElements
              }, '*');
            }
          }

          // Handle double-click for text editing
          document.querySelectorAll('.editable-element').forEach(function(element) {
            element.addEventListener('dblclick', function(e) {
              e.preventDefault();
              const elementId = this.getAttribute('data-element-id');
              if (elementId && window.parent && window.parent.postMessage) {
                window.parent.postMessage({
                  type: 'startTextEdit',
                  elementId: elementId,
                  currentText: this.textContent ? this.textContent.trim() : ''
                }, '*');
              }
            });
          });

          // Keyboard shortcuts
          document.addEventListener('keydown', function(e) {
            if (e.key === 'Delete' && selectedElements.length > 0) {
              if (window.parent && window.parent.postMessage) {
                window.parent.postMessage({
                  type: 'deleteElements',
                  elementIds: selectedElements
                }, '*');
              }
            }

            if ((e.ctrlKey || e.metaKey) && e.key === 'd') {
              e.preventDefault();
              if (selectedElements.length > 0) {
                if (window.parent && window.parent.postMessage) {
                  window.parent.postMessage({
                    type: 'duplicateElements',
                    elementIds: selectedElements
                  }, '*');
                }
              }
            }

            if ((e.ctrlKey || e.metaKey) && e.key === 'a') {
              e.preventDefault();
              const editableElements = document.querySelectorAll('.editable-element');
              const allElements = [];
              editableElements.forEach(function(el) {
                const id = el.getAttribute('data-element-id');
                if (id) allElements.push(id);
              });

              selectedElements = allElements;

              editableElements.forEach(function(el) {
                el.classList.remove('selected');
                el.classList.add('multi-selected');
              });

              if (window.parent && window.parent.postMessage) {
                window.parent.postMessage({
                  type: 'elementSelected',
                  elementId: null,
                  selectedElements: selectedElements
                }, '*');
              }
            }
          });

          // Deselect when clicking empty space
          document.addEventListener('click', function(e) {
            if (!e.target.closest('.editable-element')) {
              selectedElements = [];
              document.querySelectorAll('.editable-element').forEach(function(el) {
                el.classList.remove('selected', 'multi-selected');
              });

              if (window.parent && window.parent.postMessage) {
                window.parent.postMessage({
                  type: 'elementSelected',
                  elementId: null,
                  selectedElements: []
                }, '*');
              }
            }
          });
        </script>
      </body>
      </html>
    `
    } catch (error) {
      console.error('Error creating enhanced content:', error)
      return `
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Error</title>
        </head>
        <body style="display: flex; align-items: center; justify-content: center; height: 100vh; font-family: Arial, sans-serif;">
          <div style="text-align: center; color: #ef4444;">
            <div style="margin-bottom: 10px;">Error loading preview</div>
            <div style="font-size: 12px; color: #6b7280;">Please refresh the page</div>
          </div>
        </body>
        </html>
      `
    }
  }

  const refreshPreview = () => {
    setIsRefreshing(true)
    setTimeout(() => {
      if (iframeRef.current) {
        const content = createEnhancedContent()
        const blob = new Blob([content], { type: 'text/html' })
        iframeRef.current.src = URL.createObjectURL(blob)
      }
      setIsRefreshing(false)
    }, 500)
  }

  useEffect(() => {
    if (iframeRef.current && websiteState) {
      try {
        const content = createEnhancedContent()
        const blob = new Blob([content], { type: 'text/html' })
        iframeRef.current.src = URL.createObjectURL(blob)
      } catch (error) {
        console.error('Error creating preview content:', error)
        // Fallback content
        const fallbackContent = '<html><body><div style="padding: 20px; text-align: center;">Error loading preview</div></body></html>'
        const blob = new Blob([fallbackContent], { type: 'text/html' })
        iframeRef.current.src = URL.createObjectURL(blob)
      }
    }
  }, [websiteState, currentPage, techStack, selectedElement, selectedElements, gridEnabled, gridSize])

  // Listen for messages from iframe
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      try {
        if (!event.data || typeof event.data !== 'object') return

        if (event.data.type === 'elementSelected') {
          if (onSelectElement) {
            onSelectElement(event.data.elementId || null)
          }
          if (event.data.selectedElements && onSelectMultiple) {
            onSelectMultiple(event.data.selectedElements)
          }
        } else if (event.data.type === 'startTextEdit') {
          setEditingText(event.data.elementId || null)
          // You could open a text editing modal here
        } else if (event.data.type === 'deleteElements') {
          if (event.data.elementIds && Array.isArray(event.data.elementIds)) {
            event.data.elementIds.forEach((id: string) => {
              // Handle deletion - this would typically be handled by parent component
            })
          }
        } else if (event.data.type === 'duplicateElements') {
          if (event.data.elementIds && Array.isArray(event.data.elementIds)) {
            event.data.elementIds.forEach((id: string) => {
              // Handle duplication - this would typically be handled by parent component
            })
          }
        }
      } catch (error) {
        console.error('Error handling iframe message:', error)
      }
    }

    window.addEventListener('message', handleMessage)
    return () => window.removeEventListener('message', handleMessage)
  }, [onSelectElement, onSelectMultiple])

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 25, 200))
  }

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 25, 25))
  }

  const handleFitToScreen = () => {
    setZoom(100)
  }

  // Safety check - don't render if essential props are missing
  if (!websiteState || !onSelectElement || !onUpdateElement) {
    return (
      <div className="h-full flex items-center justify-center bg-slate-100">
        <div className="text-center">
          <div className="text-slate-500 mb-2">Loading preview...</div>
          <div className="w-6 h-6 border-2 border-slate-300 border-t-slate-600 rounded-full animate-spin mx-auto"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Preview Header */}
      <div className="flex items-center justify-between p-4 border-b border-slate-200 bg-slate-50">
        <div className="flex items-center space-x-4">
          <h3 className="text-lg font-semibold text-slate-900">Live Preview</h3>
          <div className="flex items-center space-x-2">
            <MousePointer className="h-4 w-4 text-slate-500" />
            <span className="text-sm text-slate-600">
              Click to select • Ctrl+Click for multi-select • Double-click to edit text
            </span>
          </div>
          {selectedElement && (
            <div className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
              Selected: {selectedElement}
            </div>
          )}
          {selectedElements.length > 1 && (
            <div className="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded">
              {selectedElements.length} elements selected
            </div>
          )}
        </div>

        <div className="flex items-center space-x-2">
          {/* Zoom Controls */}
          <div className="flex items-center space-x-1 bg-white rounded-lg p-1 border border-slate-200">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleZoomOut}
              disabled={zoom <= 25}
              className="p-2"
            >
              <span className="text-xs">-</span>
            </Button>
            <span className="text-xs px-2 py-1 min-w-[50px] text-center">{zoom}%</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleZoomIn}
              disabled={zoom >= 200}
              className="p-2"
            >
              <span className="text-xs">+</span>
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleFitToScreen}
              className="p-2"
              title="Fit to screen"
            >
              <Maximize className="h-3 w-3" />
            </Button>
          </div>

          {/* Viewport Controls */}
          <div className="flex items-center space-x-1 bg-white rounded-lg p-1 border border-slate-200">
            {Object.entries(viewportSizes).map(([size, config]) => (
              <button
                key={size}
                onClick={() => setViewport(size as ViewportSize)}
                className={`p-2 rounded text-xs font-medium transition-colors ${
                  viewport === size
                    ? 'bg-slate-900 text-white'
                    : 'text-slate-600'
                }`}
                title={config.label}
              >
                <config.icon className="h-4 w-4" />
              </button>
            ))}
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={refreshPreview}
            disabled={isRefreshing}
            className="bg-white"
          >
            <RotateCcw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </div>

      {/* Preview Content - Full Width */}
      <div className="flex-1 overflow-auto bg-slate-100 p-4">
        <div className="flex justify-center">
          <div
            className="bg-white border border-slate-300 rounded-lg overflow-hidden shadow-lg"
            style={{
              width: viewport === 'desktop' ? '100%' : viewportSizes[viewport].width,
              maxWidth: viewport === 'desktop' ? 'none' : viewportSizes[viewport].width,
              transform: `scale(${zoom / 100})`,
              transformOrigin: 'top center',
              transition: 'transform 0.2s ease'
            }}
          >
            <iframe
              ref={iframeRef}
              className="w-full border-0"
              style={{
                height: viewport === 'desktop' ? '800px' : viewportSizes[viewport].height,
                minHeight: '600px'
              }}
              sandbox="allow-scripts allow-same-origin"
              title="Website Preview"
            />
          </div>
        </div>
      </div>

      {/* Preview Footer */}
      <div className="flex items-center justify-between p-3 border-t border-slate-200 bg-slate-50 text-xs text-slate-500">
        <div className="flex items-center space-x-4">
          <span>Viewing in {viewportSizes[viewport].label} mode</span>
          <span>•</span>
          <span>{techStack === 'html' ? 'HTML/CSS/JS' : 'React + Tailwind'}</span>
          <span>•</span>
          <span>{currentPage?.elements.length || 0} elements</span>
        </div>
        <div className="flex items-center space-x-4">
          {gridEnabled && (
            <>
              <span>Grid: {gridSize}px</span>
              <span>•</span>
            </>
          )}
          <span>Zoom: {zoom}%</span>
        </div>
      </div>
    </div>
  )
}
