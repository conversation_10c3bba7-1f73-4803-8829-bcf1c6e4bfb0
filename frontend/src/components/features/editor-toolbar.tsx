"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  <PERSON>do, 
  <PERSON>o, 
  <PERSON><PERSON>, 
  Trash2, 
  Grid, 
  AlignLeft, 
  AlignCenter, 
  AlignRight,
  AlignJustify,
  Move,
  RotateCcw,
  ZoomIn,
  ZoomOut,
  Maximize,
  Save
} from "lucide-react"

interface EditorToolbarProps {
  onUndo: () => void
  onRedo: () => void
  canUndo: boolean
  canRedo: boolean
  selectedElement: string | null
  onDuplicate: () => void
  onDelete: () => void
  gridEnabled: boolean
  onToggleGrid: () => void
}

export function EditorToolbar({
  onUndo,
  onRedo,
  canUndo,
  canRedo,
  selectedElement,
  onDuplicate,
  onDelete,
  gridEnabled,
  onToggleGrid
}: EditorToolbarProps) {
  return (
    <div className="flex items-center space-x-1 bg-white/70 backdrop-blur-md border border-white/30 rounded-lg p-1 shadow-lg shadow-slate-200/50">
      {/* History Controls */}
      <div className="flex items-center space-x-1 pr-2 border-r border-slate-200">
        <Button
          variant="ghost"
          size="sm"
          onClick={onUndo}
          disabled={!canUndo}
          className="p-2"
          title="Undo (Ctrl+Z)"
        >
          <Undo className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={onRedo}
          disabled={!canRedo}
          className="p-2"
          title="Redo (Ctrl+Y)"
        >
          <Redo className="h-4 w-4" />
        </Button>
      </div>

      {/* Element Actions */}
      <div className="flex items-center space-x-1 pr-2 border-r border-slate-200">
        <Button
          variant="ghost"
          size="sm"
          onClick={onDuplicate}
          disabled={!selectedElement}
          className="p-2"
          title="Duplicate (Ctrl+D)"
        >
          <Copy className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={onDelete}
          disabled={!selectedElement}
          className="p-2 text-red-600"
          title="Delete (Delete)"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>

      {/* Alignment Tools */}
      <div className="flex items-center space-x-1 pr-2 border-r border-slate-200">
        <Button
          variant="ghost"
          size="sm"
          disabled={!selectedElement}
          className="p-2"
          title="Align Left"
        >
          <AlignLeft className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          disabled={!selectedElement}
          className="p-2"
          title="Align Center"
        >
          <AlignCenter className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          disabled={!selectedElement}
          className="p-2"
          title="Align Right"
        >
          <AlignRight className="h-4 w-4" />
        </Button>
      </div>

      {/* Grid and View Controls */}
      <div className="flex items-center space-x-1">
        <Button
          variant={gridEnabled ? "default" : "ghost"}
          size="sm"
          onClick={onToggleGrid}
          className="p-2"
          title="Toggle Grid (Ctrl+G)"
        >
          <Grid className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          className="p-2"
          title="Zoom In"
        >
          <ZoomIn className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          className="p-2"
          title="Zoom Out"
        >
          <ZoomOut className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          className="p-2"
          title="Fit to Screen"
        >
          <Maximize className="h-4 w-4" />
        </Button>
      </div>
    </div>
  )
}
