"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Wand2, History, Lightbulb } from "lucide-react"

interface EditPanelProps {
  onApplyEdit: (command: string) => void
  isProcessing: boolean
}

const exampleCommands = [
  "Change the background color to dark blue",
  "Make the header text larger and bold",
  "Add a contact form at the bottom",
  "Change button color to green",
  "Add a hero image placeholder",
  "Make the layout responsive",
  "Add hover effects to buttons",
  "Change font to more modern style"
]

export function EditPanel({ onApplyEdit, isProcessing }: EditPanelProps) {
  const [command, setCommand] = useState("")
  const [editHistory, setEditHistory] = useState<string[]>([])

  const handleSubmit = () => {
    if (!command.trim()) return
    
    onApplyEdit(command)
    setEditHistory(prev => [command, ...prev].slice(0, 5)) // Keep last 5 commands
    setCommand("")
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
      handleSubmit()
    }
  }

  const useExampleCommand = (exampleCommand: string) => {
    setCommand(exampleCommand)
  }

  return (
    <Card className="border-0 shadow-lg">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Wand2 className="h-5 w-5 text-purple-600 mr-2" />
          Natural Language Edits
        </CardTitle>
        <CardDescription>
          Describe the changes you want to make in plain English
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Edit Input */}
        <div>
          <label htmlFor="edit-command" className="block text-sm font-medium text-gray-700 mb-2">
            What would you like to change?
          </label>
          <Textarea
            id="edit-command"
            placeholder="e.g., Change the button color to red and make it larger..."
            value={command}
            onChange={(e) => setCommand(e.target.value)}
            onKeyDown={handleKeyPress}
            className="min-h-[80px]"
            disabled={isProcessing}
          />
          <p className="text-xs text-gray-500 mt-1">
            Press Cmd+Enter (Mac) or Ctrl+Enter (Windows) to apply quickly
          </p>
        </div>

        <Button
          onClick={handleSubmit}
          disabled={!command.trim() || isProcessing}
          className="w-full"
        >
          {isProcessing ? (
            <>
              <Wand2 className="mr-2 h-4 w-4 animate-pulse" />
              Applying Changes...
            </>
          ) : (
            <>
              Apply Changes
              <Wand2 className="ml-2 h-4 w-4" />
            </>
          )}
        </Button>

        {/* Example Commands */}
        <div>
          <h4 className="flex items-center text-sm font-medium text-gray-700 mb-3">
            <Lightbulb className="h-4 w-4 text-yellow-500 mr-1" />
            Example Commands
          </h4>
          <div className="grid grid-cols-1 gap-2">
            {exampleCommands.slice(0, 4).map((example, index) => (
              <button
                key={index}
                onClick={() => useExampleCommand(example)}
                className="text-left p-2 text-sm text-gray-600 hover:bg-blue-50 hover:text-blue-700 rounded border border-gray-200 hover:border-blue-200 transition-colors"
                disabled={isProcessing}
              >
                {example}
              </button>
            ))}
          </div>
        </div>

        {/* Edit History */}
        {editHistory.length > 0 && (
          <div>
            <h4 className="flex items-center text-sm font-medium text-gray-700 mb-3">
              <History className="h-4 w-4 text-gray-500 mr-1" />
              Recent Changes
            </h4>
            <div className="space-y-2">
              {editHistory.map((edit, index) => (
                <div
                  key={index}
                  className="p-2 bg-gray-50 rounded text-sm text-gray-600 border-l-2 border-green-400"
                >
                  {edit}
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
