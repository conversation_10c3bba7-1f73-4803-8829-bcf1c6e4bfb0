"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  Plus, 
  FileText, 
  Settings, 
  Copy, 
  Trash2,
  Home,
  User,
  Mail,
  Briefcase,
  Image as ImageIcon,
  BookOpen
} from "lucide-react"

interface WebsitePage {
  id: string
  name: string
  title: string
  slug: string
  metaDescription: string
  elements: any[]
  template?: 'home' | 'about' | 'contact' | 'services' | 'portfolio' | 'blog' | 'custom'
}

interface PageManagerProps {
  pages: WebsitePage[]
  currentPageId: string
  onSwitchPage: (pageId: string) => void
  onAddPage: (template?: WebsitePage['template']) => void
  onUpdatePage: (pageId: string, updates: Partial<WebsitePage>) => void
}

const pageTemplates = [
  { id: 'home', name: 'Home', icon: Home, description: 'Landing page with hero section' },
  { id: 'about', name: 'About', icon: User, description: 'About us page with team info' },
  { id: 'contact', name: 'Contact', icon: Mail, description: 'Contact form and information' },
  { id: 'services', name: 'Services', icon: Briefcase, description: 'Services or products showcase' },
  { id: 'portfolio', name: 'Portfolio', icon: ImageIcon, description: 'Portfolio or gallery page' },
  { id: 'blog', name: 'Blog', icon: BookOpen, description: 'Blog listing page' },
  { id: 'custom', name: 'Custom', icon: FileText, description: 'Blank page to customize' }
]

export function PageManager({
  pages,
  currentPageId,
  onSwitchPage,
  onAddPage,
  onUpdatePage
}: PageManagerProps) {
  const [showTemplates, setShowTemplates] = useState(false)
  const [editingPage, setEditingPage] = useState<string | null>(null)

  const getTemplateIcon = (template?: string) => {
    const templateData = pageTemplates.find(t => t.id === template)
    return templateData?.icon || FileText
  }

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b border-slate-200">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-semibold text-slate-900">Pages</h3>
          <Button
            size="sm"
            onClick={() => setShowTemplates(!showTemplates)}
            className="bg-slate-900 text-white"
          >
            <Plus className="h-4 w-4 mr-1" />
            Add
          </Button>
        </div>

        {showTemplates && (
          <Card className="mb-4">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Choose Template</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {pageTemplates.map((template) => {
                const Icon = template.icon
                return (
                  <button
                    key={template.id}
                    onClick={() => {
                      onAddPage(template.id as WebsitePage['template'])
                      setShowTemplates(false)
                    }}
                    className="w-full flex items-start space-x-3 p-2 rounded border border-slate-200 text-left text-sm"
                  >
                    <Icon className="h-4 w-4 text-slate-500 mt-0.5 flex-shrink-0" />
                    <div>
                      <div className="font-medium text-slate-900">{template.name}</div>
                      <div className="text-xs text-slate-500">{template.description}</div>
                    </div>
                  </button>
                )
              })}
            </CardContent>
          </Card>
        )}
      </div>

      <div className="flex-1 overflow-y-auto p-4 space-y-2">
        {pages.map((page) => {
          const Icon = getTemplateIcon(page.template)
          const isActive = page.id === currentPageId
          const isEditing = editingPage === page.id

          return (
            <div
              key={page.id}
              className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                isActive 
                  ? 'bg-slate-900 text-white border-slate-900' 
                  : 'bg-white border-slate-200'
              }`}
            >
              {isEditing ? (
                <div className="space-y-2">
                  <Input
                    value={page.name}
                    onChange={(e) => onUpdatePage(page.id, { name: e.target.value })}
                    className="text-sm"
                    placeholder="Page name"
                  />
                  <Input
                    value={page.slug}
                    onChange={(e) => onUpdatePage(page.id, { slug: e.target.value })}
                    className="text-sm"
                    placeholder="URL slug"
                  />
                  <div className="flex space-x-1">
                    <Button
                      size="sm"
                      onClick={() => setEditingPage(null)}
                      className="flex-1"
                    >
                      Save
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setEditingPage(null)}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              ) : (
                <div onClick={() => onSwitchPage(page.id)}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Icon className={`h-4 w-4 ${isActive ? 'text-white' : 'text-slate-500'}`} />
                      <div>
                        <div className={`font-medium text-sm ${isActive ? 'text-white' : 'text-slate-900'}`}>
                          {page.name}
                        </div>
                        <div className={`text-xs ${isActive ? 'text-slate-300' : 'text-slate-500'}`}>
                          {page.slug}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={(e) => {
                          e.stopPropagation()
                          setEditingPage(page.id)
                        }}
                        className="p-1"
                      >
                        <Settings className="h-3 w-3" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={(e) => {
                          e.stopPropagation()
                          // Duplicate page logic
                        }}
                        className="p-1"
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                      {pages.length > 1 && (
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={(e) => {
                            e.stopPropagation()
                            // Delete page logic
                          }}
                          className="p-1 text-red-600"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                  </div>
                  
                  <div className={`text-xs mt-1 ${isActive ? 'text-slate-300' : 'text-slate-500'}`}>
                    {page.elements.length} elements
                  </div>
                </div>
              )}
            </div>
          )
        })}
      </div>
    </div>
  )
}
