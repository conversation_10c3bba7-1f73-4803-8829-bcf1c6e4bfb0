"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { MessageSquare, Send, User, Clock, MapPin } from "lucide-react"
import { Comment } from "@/types"

interface CommentSystemProps {
  comments: Comment[]
  onAddComment: (comment: Omit<Comment, 'id' | 'createdAt'>) => void
  isReadOnly?: boolean
}

export function CommentSystem({ comments, onAddComment, isReadOnly = false }: CommentSystemProps) {
  const [newComment, setNewComment] = useState("")
  const [authorName, setAuthorName] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async () => {
    if (!newComment.trim() || !authorName.trim()) return

    setIsSubmitting(true)

    try {
      await onAddComment({
        projectId: 'current-project', // This would be passed from parent
        author: authorName,
        content: newComment
      })
      
      setNewComment("")
      // Don't clear author name for convenience
    } catch (error) {
      console.error('Error adding comment:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date)
  }

  return (
    <Card className="border-0 shadow-lg">
      <CardHeader>
        <CardTitle className="flex items-center">
          <MessageSquare className="h-5 w-5 text-blue-600 mr-2" />
          Comments & Feedback
        </CardTitle>
        <CardDescription>
          {isReadOnly 
            ? "Share your thoughts about this website design"
            : "Client feedback and comments"
          }
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Add Comment Form */}
        {!isReadOnly && (
          <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="author-name" className="block text-sm font-medium text-gray-700 mb-1">
                  Your Name
                </label>
                <Input
                  id="author-name"
                  placeholder="Enter your name"
                  value={authorName}
                  onChange={(e) => setAuthorName(e.target.value)}
                  disabled={isSubmitting}
                />
              </div>
            </div>
            
            <div>
              <label htmlFor="comment-content" className="block text-sm font-medium text-gray-700 mb-1">
                Your Feedback
              </label>
              <Textarea
                id="comment-content"
                placeholder="Share your thoughts about the design, suggest changes, or ask questions..."
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                className="min-h-[100px]"
                disabled={isSubmitting}
              />
            </div>

            <Button
              onClick={handleSubmit}
              disabled={!newComment.trim() || !authorName.trim() || isSubmitting}
              className="w-full"
            >
              {isSubmitting ? (
                <>
                  <MessageSquare className="mr-2 h-4 w-4 animate-pulse" />
                  Posting Comment...
                </>
              ) : (
                <>
                  Post Comment
                  <Send className="ml-2 h-4 w-4" />
                </>
              )}
            </Button>
          </div>
        )}

        {/* Comments List */}
        <div className="space-y-4">
          {comments.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No comments yet. Be the first to share feedback!</p>
            </div>
          ) : (
            comments.map((comment) => (
              <div
                key={comment.id}
                className="border border-gray-200 rounded-lg p-4 bg-white"
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                      <User className="h-4 w-4 text-blue-600" />
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">{comment.author}</h4>
                      <div className="flex items-center text-sm text-gray-500">
                        <Clock className="h-3 w-3 mr-1" />
                        {formatDate(comment.createdAt)}
                        {comment.position && (
                          <>
                            <span className="mx-2">•</span>
                            <MapPin className="h-3 w-3 mr-1" />
                            Position marked
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
                
                <p className="text-gray-700 leading-relaxed">{comment.content}</p>
                
                {comment.position && (
                  <div className="mt-3 p-2 bg-blue-50 rounded text-sm text-blue-700">
                    💡 This comment refers to a specific location on the page
                  </div>
                )}
              </div>
            ))
          )}
        </div>

        {/* Comments Summary */}
        {comments.length > 0 && (
          <div className="pt-4 border-t border-gray-200">
            <p className="text-sm text-gray-600 text-center">
              {comments.length} comment{comments.length !== 1 ? 's' : ''} total
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
