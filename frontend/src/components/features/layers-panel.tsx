"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { 
  <PERSON><PERSON>, 
  Eye, 
  <PERSON>Off, 
  <PERSON>, 
  Un<PERSON>,
  Copy,
  Trash2,
  GripVertical,
  Type,
  Image as ImageIcon,
  Square,
  MousePointer,
  ChevronDown,
  ChevronRight
} from "lucide-react"

interface WebsiteElement {
  id: string
  type: 'text' | 'image' | 'button' | 'section' | 'container' | 'header' | 'footer'
  content: string
  styles: Record<string, string>
  position: { x: number; y: number; width: number; height: number }
  parent?: string
  children?: string[]
  locked?: boolean
  visible?: boolean
}

interface LayersPanelProps {
  elements: WebsiteElement[]
  selectedElement: string | null
  selectedElements: string[]
  onSelectElement: (elementId: string | null) => void
  onSelectMultiple: (elementIds: string[]) => void
  onUpdateElement: (elementId: string, updates: Partial<WebsiteElement>) => void
  onDuplicateElement: (elementId: string) => void
  onDeleteElement: (elementId: string) => void
  onReorderElements: (elementIds: string[]) => void
}

const getElementIcon = (type: string) => {
  switch (type) {
    case 'text': return Type
    case 'image': return ImageIcon
    case 'button': return MousePointer
    case 'section': return Square
    case 'container': return Square
    case 'header': return Square
    case 'footer': return Square
    default: return Square
  }
}

export function LayersPanel({
  elements,
  selectedElement,
  selectedElements,
  onSelectElement,
  onSelectMultiple,
  onUpdateElement,
  onDuplicateElement,
  onDeleteElement,
  onReorderElements
}: LayersPanelProps) {
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set())
  const [draggedElement, setDraggedElement] = useState<string | null>(null)

  const toggleGroup = (groupId: string) => {
    const newExpanded = new Set(expandedGroups)
    if (newExpanded.has(groupId)) {
      newExpanded.delete(groupId)
    } else {
      newExpanded.add(groupId)
    }
    setExpandedGroups(newExpanded)
  }

  const handleDragStart = (e: React.DragEvent, elementId: string) => {
    setDraggedElement(elementId)
    e.dataTransfer.effectAllowed = 'move'
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    e.dataTransfer.dropEffect = 'move'
  }

  const handleDrop = (e: React.DragEvent, targetElementId: string) => {
    e.preventDefault()
    if (!draggedElement || draggedElement === targetElementId) return

    const draggedIndex = elements.findIndex(el => el.id === draggedElement)
    const targetIndex = elements.findIndex(el => el.id === targetElementId)
    
    if (draggedIndex === -1 || targetIndex === -1) return

    const newElements = [...elements]
    const [draggedEl] = newElements.splice(draggedIndex, 1)
    newElements.splice(targetIndex, 0, draggedEl)
    
    onReorderElements(newElements.map(el => el.id))
    setDraggedElement(null)
  }

  const toggleVisibility = (elementId: string) => {
    const element = elements.find(el => el.id === elementId)
    if (element) {
      onUpdateElement(elementId, { visible: !element.visible })
    }
  }

  const toggleLock = (elementId: string) => {
    const element = elements.find(el => el.id === elementId)
    if (element) {
      onUpdateElement(elementId, { locked: !element.locked })
    }
  }

  // Group elements by type for better organization
  const groupedElements = elements.reduce((groups, element) => {
    const group = element.type
    if (!groups[group]) {
      groups[group] = []
    }
    groups[group].push(element)
    return groups
  }, {} as Record<string, WebsiteElement[]>)

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b border-white/20">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Layers className="h-4 w-4 text-slate-600" />
            <h3 className="font-semibold text-slate-900">Layers</h3>
          </div>
          <span className="text-xs text-slate-500 bg-white/50 backdrop-blur-sm px-2 py-1 rounded-full border border-white/30">{elements.length}</span>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto p-2">
        {Object.entries(groupedElements).map(([groupType, groupElements]) => {
          const isExpanded = expandedGroups.has(groupType)
          const GroupIcon = getElementIcon(groupType)
          
          return (
            <div key={groupType} className="mb-2">
              <button
                onClick={() => toggleGroup(groupType)}
                className="w-full flex items-center space-x-2 p-2 text-left text-sm font-medium text-slate-700 rounded"
              >
                {isExpanded ? (
                  <ChevronDown className="h-3 w-3" />
                ) : (
                  <ChevronRight className="h-3 w-3" />
                )}
                <GroupIcon className="h-4 w-4" />
                <span className="capitalize">{groupType}s ({groupElements.length})</span>
              </button>

              {isExpanded && (
                <div className="ml-4 space-y-1">
                  {groupElements.map((element) => {
                    const Icon = getElementIcon(element.type)
                    const isSelected = selectedElement === element.id
                    const isVisible = element.visible !== false
                    const isLocked = element.locked === true

                    return (
                      <div
                        key={element.id}
                        draggable={!isLocked}
                        onDragStart={(e) => handleDragStart(e, element.id)}
                        onDragOver={handleDragOver}
                        onDrop={(e) => handleDrop(e, element.id)}
                        className={`group flex items-center space-x-2 p-2 rounded cursor-pointer transition-all duration-200 ${
                          isSelected
                            ? 'bg-slate-900/80 backdrop-blur-sm text-white border border-slate-700/50 shadow-lg shadow-slate-900/30'
                            : 'hover:bg-white/60 backdrop-blur-sm border border-white/30 shadow-md shadow-slate-200/20'
                        }`}
                        onClick={() => onSelectElement(element.id)}
                      >
                        <GripVertical className={`h-3 w-3 ${isSelected ? 'text-white' : 'text-slate-400'}`} />
                        <Icon className={`h-4 w-4 ${isSelected ? 'text-white' : 'text-slate-500'}`} />
                        
                        <div className="flex-1 min-w-0">
                          <div className={`text-sm font-medium truncate ${isSelected ? 'text-white' : 'text-slate-900'}`}>
                            {element.content.substring(0, 20) || `${element.type} element`}
                          </div>
                          <div className={`text-xs truncate ${isSelected ? 'text-slate-300' : 'text-slate-500'}`}>
                            {element.id}
                          </div>
                        </div>

                        <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100">
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={(e) => {
                              e.stopPropagation()
                              toggleVisibility(element.id)
                            }}
                            className="p-1"
                          >
                            {isVisible ? (
                              <Eye className="h-3 w-3" />
                            ) : (
                              <EyeOff className="h-3 w-3" />
                            )}
                          </Button>
                          
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={(e) => {
                              e.stopPropagation()
                              toggleLock(element.id)
                            }}
                            className="p-1"
                          >
                            {isLocked ? (
                              <Lock className="h-3 w-3" />
                            ) : (
                              <Unlock className="h-3 w-3" />
                            )}
                          </Button>

                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={(e) => {
                              e.stopPropagation()
                              onDuplicateElement(element.id)
                            }}
                            className="p-1"
                          >
                            <Copy className="h-3 w-3" />
                          </Button>

                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={(e) => {
                              e.stopPropagation()
                              onDeleteElement(element.id)
                            }}
                            className="p-1 text-red-600"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    )
                  })}
                </div>
              )}
            </div>
          )
        })}

        {elements.length === 0 && (
          <div className="text-center py-8 text-slate-500">
            <Layers className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No elements on this page</p>
          </div>
        )}
      </div>
    </div>
  )
}
