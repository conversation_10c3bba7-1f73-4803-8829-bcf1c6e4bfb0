"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  Wand2, 
  Palette, 
  Type, 
  Layout, 
  Image as ImageIcon, 
  Settings,
  Plus,
  Trash2,
  Move,
  Copy
} from "lucide-react"

interface WebsiteElement {
  id: string
  type: 'text' | 'image' | 'button' | 'section'
  content: string
  styles: Record<string, string>
  position: { x: number; y: number; width: number; height: number }
}

interface WebsiteState {
  elements: WebsiteElement[]
  globalStyles: {
    backgroundColor: string
    primaryColor: string
    secondaryColor: string
    textColor: string
    fontFamily: string
    fontSize: string
  }
  layout: {
    spacing: string
    padding: string
    margin: string
  }
}

interface ComprehensiveEditPanelProps {
  websiteState: WebsiteState
  selectedElement: string | null
  onUpdateElement: (elementId: string, updates: Partial<WebsiteElement>) => void
  onUpdateGlobalStyles: (updates: Partial<WebsiteState['globalStyles']>) => void
  onUpdateLayout: (updates: Partial<WebsiteState['layout']>) => void
  onApplyEdit: (command: string) => void
  isProcessing: boolean
}

const colorPresets = [
  { name: 'Blue', primary: '#3b82f6', secondary: '#1e40af' },
  { name: 'Purple', primary: '#8b5cf6', secondary: '#7c3aed' },
  { name: 'Green', primary: '#10b981', secondary: '#059669' },
  { name: 'Red', primary: '#ef4444', secondary: '#dc2626' },
  { name: 'Orange', primary: '#f97316', secondary: '#ea580c' },
  { name: 'Pink', primary: '#ec4899', secondary: '#db2777' }
]

const fontOptions = [
  'Arial, sans-serif',
  'Georgia, serif',
  'Times New Roman, serif',
  'Helvetica, sans-serif',
  'Verdana, sans-serif',
  'Courier New, monospace'
]

export function ComprehensiveEditPanel({
  websiteState,
  selectedElement,
  onUpdateElement,
  onUpdateGlobalStyles,
  onUpdateLayout,
  onApplyEdit,
  isProcessing
}: ComprehensiveEditPanelProps) {
  const [command, setCommand] = useState("")
  const [activeTab, setActiveTab] = useState("styles")

  const handleSubmit = () => {
    if (!command.trim()) return
    onApplyEdit(command)
    setCommand("")
  }

  const selectedElementData = selectedElement 
    ? websiteState.elements.find(el => el.id === selectedElement)
    : null

  return (
    <div className="space-y-4">
      {/* Quick Actions */}
      <Card className="border-slate-200 shadow-sm">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center text-lg">
            <Wand2 className="h-5 w-5 text-slate-600 mr-2" />
            Quick Edit
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <Textarea
              placeholder="Describe changes in plain English..."
              value={command}
              onChange={(e) => setCommand(e.target.value)}
              className="min-h-[80px] text-sm"
              disabled={isProcessing}
            />
            <Button
              onClick={handleSubmit}
              disabled={!command.trim() || isProcessing}
              className="w-full"
              size="sm"
            >
              {isProcessing ? (
                <>
                  <Wand2 className="mr-2 h-4 w-4 animate-pulse" />
                  Applying...
                </>
              ) : (
                <>
                  Apply Changes
                  <Wand2 className="ml-2 h-4 w-4" />
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Editing Tabs */}
      <Card className="border-slate-200 shadow-sm">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">Visual Editor</CardTitle>
          <CardDescription>
            {selectedElement ? `Editing: ${selectedElement}` : 'Select an element to edit'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="styles" className="text-xs">
                <Palette className="h-3 w-3 mr-1" />
                Colors
              </TabsTrigger>
              <TabsTrigger value="typography" className="text-xs">
                <Type className="h-3 w-3 mr-1" />
                Text
              </TabsTrigger>
              <TabsTrigger value="layout" className="text-xs">
                <Layout className="h-3 w-3 mr-1" />
                Layout
              </TabsTrigger>
              <TabsTrigger value="content" className="text-xs">
                <Settings className="h-3 w-3 mr-1" />
                Content
              </TabsTrigger>
            </TabsList>

            <TabsContent value="styles" className="space-y-4 mt-4">
              <div>
                <Label className="text-sm font-medium">Color Themes</Label>
                <div className="grid grid-cols-2 gap-2 mt-2">
                  {colorPresets.map((preset) => (
                    <button
                      key={preset.name}
                      onClick={() => onUpdateGlobalStyles({
                        primaryColor: preset.primary,
                        secondaryColor: preset.secondary
                      })}
                      className="flex items-center space-x-2 p-2 rounded border border-slate-200 text-left text-sm"
                    >
                      <div className="flex space-x-1">
                        <div 
                          className="w-3 h-3 rounded-full" 
                          style={{ backgroundColor: preset.primary }}
                        />
                        <div 
                          className="w-3 h-3 rounded-full" 
                          style={{ backgroundColor: preset.secondary }}
                        />
                      </div>
                      <span>{preset.name}</span>
                    </button>
                  ))}
                </div>
              </div>

              <div className="space-y-3">
                <div>
                  <Label className="text-sm font-medium">Background Color</Label>
                  <Input
                    type="color"
                    value={websiteState.globalStyles.backgroundColor}
                    onChange={(e) => onUpdateGlobalStyles({ backgroundColor: e.target.value })}
                    className="h-8 w-full mt-1"
                  />
                </div>
                <div>
                  <Label className="text-sm font-medium">Primary Color</Label>
                  <Input
                    type="color"
                    value={websiteState.globalStyles.primaryColor}
                    onChange={(e) => onUpdateGlobalStyles({ primaryColor: e.target.value })}
                    className="h-8 w-full mt-1"
                  />
                </div>
                <div>
                  <Label className="text-sm font-medium">Text Color</Label>
                  <Input
                    type="color"
                    value={websiteState.globalStyles.textColor}
                    onChange={(e) => onUpdateGlobalStyles({ textColor: e.target.value })}
                    className="h-8 w-full mt-1"
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="typography" className="space-y-4 mt-4">
              <div>
                <Label className="text-sm font-medium">Font Family</Label>
                <select
                  value={websiteState.globalStyles.fontFamily}
                  onChange={(e) => onUpdateGlobalStyles({ fontFamily: e.target.value })}
                  className="w-full mt-1 p-2 border border-slate-200 rounded text-sm"
                >
                  {fontOptions.map((font) => (
                    <option key={font} value={font}>{font.split(',')[0]}</option>
                  ))}
                </select>
              </div>

              <div>
                <Label className="text-sm font-medium">Base Font Size</Label>
                <Input
                  type="range"
                  min="12"
                  max="24"
                  value={parseInt(websiteState.globalStyles.fontSize)}
                  onChange={(e) => onUpdateGlobalStyles({ fontSize: `${e.target.value}px` })}
                  className="mt-1"
                />
                <div className="text-xs text-slate-500 mt-1">
                  {websiteState.globalStyles.fontSize}
                </div>
              </div>

              {selectedElementData && (
                <div className="border-t pt-3">
                  <Label className="text-sm font-medium">Selected Element Text</Label>
                  <Textarea
                    value={selectedElementData.content}
                    onChange={(e) => onUpdateElement(selectedElement!, { content: e.target.value })}
                    className="mt-1 text-sm"
                    rows={3}
                  />
                </div>
              )}
            </TabsContent>

            <TabsContent value="layout" className="space-y-4 mt-4">
              <div>
                <Label className="text-sm font-medium">Section Padding</Label>
                <select
                  value={websiteState.layout.padding}
                  onChange={(e) => onUpdateLayout({ padding: e.target.value })}
                  className="w-full mt-1 p-2 border border-slate-200 rounded text-sm"
                >
                  <option value="1rem">Small (1rem)</option>
                  <option value="2rem">Medium (2rem)</option>
                  <option value="3rem">Large (3rem)</option>
                  <option value="4rem">Extra Large (4rem)</option>
                </select>
              </div>

              <div>
                <Label className="text-sm font-medium">Element Spacing</Label>
                <select
                  value={websiteState.layout.spacing}
                  onChange={(e) => onUpdateLayout({ spacing: e.target.value })}
                  className="w-full mt-1 p-2 border border-slate-200 rounded text-sm"
                >
                  <option value="0.5rem">Tight (0.5rem)</option>
                  <option value="1rem">Normal (1rem)</option>
                  <option value="1.5rem">Relaxed (1.5rem)</option>
                  <option value="2rem">Loose (2rem)</option>
                </select>
              </div>
            </TabsContent>

            <TabsContent value="content" className="space-y-4 mt-4">
              <div className="space-y-2">
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Section
                </Button>
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <ImageIcon className="h-4 w-4 mr-2" />
                  Add Image
                </Button>
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <Type className="h-4 w-4 mr-2" />
                  Add Text Block
                </Button>
              </div>

              {selectedElement && (
                <div className="border-t pt-3 space-y-2">
                  <Label className="text-sm font-medium">Element Actions</Label>
                  <div className="space-y-2">
                    <Button variant="outline" size="sm" className="w-full justify-start">
                      <Copy className="h-4 w-4 mr-2" />
                      Duplicate
                    </Button>
                    <Button variant="outline" size="sm" className="w-full justify-start">
                      <Move className="h-4 w-4 mr-2" />
                      Move
                    </Button>
                    <Button variant="outline" size="sm" className="w-full justify-start text-red-600">
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete
                    </Button>
                  </div>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
