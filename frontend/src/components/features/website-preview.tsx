"use client"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Monitor, Smartphone, Tablet, Maximize, RotateCcw } from "lucide-react"

interface WebsitePreviewProps {
  generatedCode: string
  techStack: 'html' | 'react'
}

type ViewportSize = 'desktop' | 'tablet' | 'mobile'

const viewportSizes = {
  desktop: { width: '100%', height: '600px', icon: Monitor },
  tablet: { width: '768px', height: '600px', icon: Tablet },
  mobile: { width: '375px', height: '600px', icon: Smartphone }
}

export function WebsitePreview({ generatedCode, techStack }: WebsitePreviewProps) {
  const [viewport, setViewport] = useState<ViewportSize>('desktop')
  const [isRefreshing, setIsRefreshing] = useState(false)
  const iframeRef = useRef<HTMLIFrameElement>(null)

  const createSandboxContent = () => {
    if (techStack === 'html') {
      return `
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Generated Website</title>
          <style>
            body { margin: 0; padding: 0; font-family: Arial, sans-serif; }
            ${generatedCode.includes('<style>') ? '' : generatedCode}
          </style>
        </head>
        <body>
          ${generatedCode.includes('<body>') ? generatedCode.split('<body>')[1]?.split('</body>')[0] : generatedCode}
        </body>
        </html>
      `
    } else {
      // For React, we'd need a more complex setup with Babel/transpilation
      // For now, return a simulated React preview
      return `
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Generated Website</title>
          <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
          <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
          <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
          <script src="https://cdn.tailwindcss.com"></script>
          <style>
            body { margin: 0; padding: 0; }
          </style>
        </head>
        <body>
          <div id="root"></div>
          <script type="text/babel">
            ${generatedCode}
          </script>
        </body>
        </html>
      `
    }
  }

  const refreshPreview = () => {
    setIsRefreshing(true)
    setTimeout(() => {
      if (iframeRef.current) {
        iframeRef.current.src = 'about:blank'
        setTimeout(() => {
          if (iframeRef.current) {
            const content = createSandboxContent()
            const blob = new Blob([content], { type: 'text/html' })
            iframeRef.current.src = URL.createObjectURL(blob)
          }
          setIsRefreshing(false)
        }, 100)
      }
    }, 500)
  }

  useEffect(() => {
    if (iframeRef.current) {
      const content = createSandboxContent()
      const blob = new Blob([content], { type: 'text/html' })
      iframeRef.current.src = URL.createObjectURL(blob)
    }
  }, [generatedCode, techStack])

  return (
    <Card className="border-0 shadow-lg">
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">Website Preview</h3>
          
          <div className="flex items-center space-x-2">
            {/* Viewport Controls */}
            <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
              {Object.entries(viewportSizes).map(([size, config]) => (
                <button
                  key={size}
                  onClick={() => setViewport(size as ViewportSize)}
                  className={`p-2 rounded ${
                    viewport === size
                      ? 'bg-white shadow-sm text-blue-600'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <config.icon className="h-4 w-4" />
                </button>
              ))}
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={refreshPreview}
              disabled={isRefreshing}
            >
              <RotateCcw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
      </div>

      <div className="p-4">
        <div className="flex justify-center">
          <div
            className="border border-gray-300 rounded-lg overflow-hidden shadow-sm transition-all duration-300"
            style={{
              width: viewportSizes[viewport].width,
              maxWidth: '100%'
            }}
          >
            <iframe
              ref={iframeRef}
              className="w-full"
              style={{ height: viewportSizes[viewport].height }}
              sandbox="allow-scripts allow-same-origin"
              title="Website Preview"
            />
          </div>
        </div>

        <div className="flex justify-center mt-4">
          <p className="text-sm text-gray-500">
            Viewing in {viewport} mode • {techStack === 'html' ? 'HTML/CSS/JS' : 'React + Tailwind'}
          </p>
        </div>
      </div>
    </Card>
  )
}
