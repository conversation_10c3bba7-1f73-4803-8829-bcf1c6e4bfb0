MVP Development PRD – AI SiteForge

Product Name: AI SiteForge
Owner: You
Date: Aug 13, 2025
Version: 1.0

⸻

1. Objective

Deliver a functional MVP of AI SiteForge that demonstrates the core value: Generate, preview, and export a website from a text prompt and/or screenshot, with tech stack choice and basic client review capabilities.

⸻

2. <PERSON><PERSON> (MVP)

Included in MVP:
	1.	User Input – Prompt box for natural language and screenshot upload.
	2.	AI Generation – Convert prompt/screenshot to HTML/CSS/JS with basic layout accuracy.
	3.	Tech Stack Choice – Support for:
	•	HTML/CSS/Vanilla JS
	•	React + TailwindCSS
	4.	Preview Environment – Render generated site in a secure iframe/sandbox.
	5.	Natural Language Edits – Basic adjustments via text commands.
	6.	Client Review Portal – Secure link for clients to preview and comment.
	7.	Export & Payment – Stripe integration for one-time export purchase.

Excluded from MVP (Future Releases):
	•	Multi-language site generation.
	•	Asset cleanup/upscaling.
	•	Performance optimization reports.
	•	Component library extraction.
	•	Voice commands.

⸻

3. User Flow (MVP)
	1.	Landing Page – CTA to start.
	2.	Input Screen – Enter prompt + optional screenshot upload.
	3.	Stack Selection – Choose between supported options.
	4.	Generation Process – AI creates abstract layout → framework-specific code.
	5.	Preview – Live sandbox view.
	6.	Edit Commands – Apply small changes via text.
	7.	Client Review – Share link, client can comment.
	8.	Payment & Export – User pays, downloads zip or deploys to Netlify/Vercel.

⸻

4. Technical Requirements
	•	Frontend: Next.js (UI, routing, preview embedding).
	•	Backend: Node.js/Express (API, AI calls, payment handling).
	•	AI Models:
	•	Multi-modal LLM for prompt + screenshot parsing.
	•	CV model for layout detection.
	•	Sandbox: CodeSandbox or custom iframe runner.
	•	Payments: Stripe Checkout.
	•	Database: Supabase/Postgres for projects, users, comments.
	•	Auth: Email-based or magic link for project owner and client access.

⸻

5. Success Metrics
	•	Generation time < 20 seconds.
	•	50% of new users reach preview stage.
	•	10% conversion to paid export.
	•	Client review completion rate >60% for agency projects.

⸻

6. Timeline (MVP)

Phase	Task	Duration
1	Model research & integration	2 weeks
2	Input screen + stack selection UI	2 weeks
3	AI → HTML/CSS/JS generation pipeline	4 weeks
4	Preview sandbox implementation	2 weeks
5	Basic natural language edit commands	2 weeks
6	Client review portal (basic comments)	3 weeks
7	Payment & export system	1 week
8	Testing & bug fixes	2 weeks
Total	MVP Launch	~18 weeks


⸻

7. Risks
	•	AI generation inaccuracies requiring post-edit.
	•	Screenshot parsing limitations.
	•	Delays due to sandbox integration complexity.
	•	Security of shared client review links.

⸻

8. Deliverables
	•	Fully functional MVP accessible via web browser.
	•	Ability to generate, preview, edit, review, and export websites in supported stacks.
	•	Documentation for setup, deployment, and AI API usage.